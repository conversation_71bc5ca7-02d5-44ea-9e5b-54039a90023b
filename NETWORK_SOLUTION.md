# 网络连接问题解决方案

## 问题分析

根据用户反馈的日志信息，主要问题是：
```
[WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
```

这是典型的网络连接超时问题，通常由以下原因造成：
1. 网络连接不稳定
2. 防火墙阻止连接
3. 无法访问Google服务
4. 网络代理设置问题

## 解决方案

### 方案1：使用简化版本程序（推荐）

我们创建了一个专门优化网络连接的简化版本：

```bash
python simple_main.py
```

**特点：**
- 内置网络连接测试功能
- 优化的重试机制（最多5次重试）
- 更好的错误处理和用户提示
- 音频压缩优化（32k比特率，MP3编码）

### 方案2：使用调试版本获取详细信息

```bash
python debug_main.py
```

**用途：**
- 获取详细的处理日志
- 诊断具体的失败原因
- 记录到debug.log文件

### 方案3：网络环境检查

运行网络诊断工具：
```bash
python test_network.py
```

**检查项目：**
- 基本网络连接
- Google服务访问
- API连接测试
- 文件上传测试

## 网络环境优化建议

### 1. 检查网络连接
- 确保网络连接稳定
- 测试访问Google服务（如google.com）
- 检查网络速度是否足够

### 2. 防火墙设置
- 检查Windows防火墙设置
- 确保Python程序被允许访问网络
- 临时关闭防火墙测试（不推荐长期使用）

### 3. 使用VPN（推荐）
如果在中国大陆使用，建议：
- 使用稳定的VPN服务
- 选择美国或欧洲的服务器
- 确保VPN支持Google服务访问

### 4. 代理设置
如果使用公司网络：
- 检查代理设置
- 配置正确的代理服务器
- 确保代理支持HTTPS连接

## 音频文件优化

### 当前优化措施
- **采样率**: 降低到16kHz
- **比特率**: 降低到32k
- **编码格式**: 使用MP3压缩
- **文件大小**: 通常可减少70-80%

### 进一步优化建议
1. **使用较短的视频**：建议单个视频不超过5分钟
2. **预处理视频**：使用视频编辑软件预先压缩
3. **分段处理**：将长视频分割成多个短片段

## 使用步骤（简化版本）

### 1. 启动程序
```bash
python simple_main.py
```

### 2. 测试网络
- 点击"测试网络连接"按钮
- 等待测试完成
- 确保显示"网络连接正常"

### 3. 配置设置
- 输入有效的Gemini API密钥
- 选择包含视频文件的文件夹

### 4. 开始处理
- 点击"开始生成字幕"
- 观察日志输出
- 等待处理完成

## 常见错误及解决方案

### 错误1：连接超时
**解决方案：**
- 使用VPN
- 检查防火墙设置
- 尝试不同的网络环境

### 错误2：API密钥错误
**解决方案：**
- 验证API密钥是否正确
- 检查API密钥是否有效
- 确认有API使用权限

### 错误3：文件上传失败
**解决方案：**
- 使用更短的视频文件
- 检查网络稳定性
- 尝试多次重试

### 错误4：配额不足
**解决方案：**
- 检查Google AI Studio中的配额使用情况
- 等待配额重置
- 考虑升级API计划

## 技术改进详情

### 网络连接优化
1. **重试机制**：指数退避策略，最多5次重试
2. **超时控制**：合理的超时时间设置
3. **连接检查**：启动时验证网络连接
4. **错误分类**：区分不同类型的网络错误

### 音频处理优化
1. **压缩算法**：使用MP3编码减小文件大小
2. **参数调优**：优化采样率和比特率
3. **大小检查**：处理前后验证文件大小
4. **分段支持**：为长音频提供分段处理

### 用户体验改进
1. **详细日志**：实时显示处理进度
2. **错误提示**：提供具体的解决建议
3. **网络测试**：内置网络诊断功能
4. **简化界面**：专注于核心功能

## 联系支持

如果以上方案都无法解决问题，请提供：
1. 运行 `python test_network.py` 的完整输出
2. debug.log 文件内容
3. 网络环境描述（是否使用VPN、代理等）
4. 具体的错误信息截图

## 总结

网络连接问题是使用Google API时的常见问题，特别是在网络环境受限的情况下。通过使用我们提供的优化版本程序和网络环境配置建议，应该能够解决大部分连接问题。

**推荐使用顺序：**
1. 首先尝试简化版本 (`python simple_main.py`)
2. 如果仍有问题，运行网络测试 (`python test_network.py`)
3. 根据测试结果调整网络环境（VPN、防火墙等）
4. 使用调试版本获取详细日志 (`python debug_main.py`)
