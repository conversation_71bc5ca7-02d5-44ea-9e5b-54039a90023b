# 视频字幕自动生成器

## 项目目标

创建一个Python桌面应用程序，用户可以通过图形界面（UI）选择一个包含视频的文件夹。程序会自动为文件夹内所有视频提取音频，调用Gemini API生成中英双语字幕，并将字幕文件（`.srt`格式）保存在原视频文件夹中，文件名与视频文件相同。

## 工作流程

```mermaid
graph TD
    A[启动应用程序] --> B{显示UI界面};
    B --> C[用户选择视频文件夹];
    C --> D{遍历文件夹内的视频文件};
    D -- 找到一个视频 --> E[提取视频的音频];
    E --> F[调用Gemini API进行语音转文字];
    F --> G[获取中文字幕];
    F --> H[获取英文字幕];
    G & H --> I[合并生成双语.srt字幕文件];
    I --> J{保存字幕文件到原文件夹};
    J --> K{还有其他视频吗?};
    K -- 是 --> D;
    K -- 否 --> L[在UI界面显示完成消息];
    L --> M[任务结束];
```

---

## 详细实施步骤

### 第一阶段：项目设置与环境准备

1.  **创建项目结构**:
    *   主程序文件: `main.py`
    *   依赖管理文件: `requirements.txt`

2.  **安装必要的Python库**:
    *   **`google-generativeai`**: 用于与Google Gemini API进行交互。
    *   **`moviepy`**: 用于从视频中提取音频。
    *   **`tkinter`**: Python内置的GUI库。

### 第二阶段：UI界面设计 (使用Tkinter)

1.  **创建主窗口**并设置标题和大小。
2.  **添加UI组件**:
    *   **文件夹选择按钮**
    *   **路径显示标签**
    *   **API密钥输入框** (可选，但推荐)
    *   **开始生成按钮**
    *   **状态/日志显示区域**

### 第三阶段：核心功能实现

1.  **文件夹处理逻辑**:
    *   实现文件夹选择功能。
    *   遍历文件夹并筛选视频文件。

2.  **音频提取**:
    *   使用 `moviepy` 从视频中提取音轨并保存为临时音频文件。

3.  **与Gemini API交互**:
    *   配置并初始化API。
    *   上传音频文件。
    *   发送请求进行语音转录（带时间戳）。
    *   发送请求进行文本翻译。

4.  **SRT字幕文件生成**:
    *   根据SRT格式标准，组合中英双语字幕。
    *   将字幕内容写入与视频同名的 `.srt` 文件中。

### 第四阶段：整合与优化

1.  **主流程控制**:
    *   将所有功能整合到UI事件中。
    *   添加错误处理机制。
    *   在处理期间禁用按钮以防重复操作。

2.  **多线程**:
    *   将耗时的处理任务放入子线程，防止UI卡顿。
