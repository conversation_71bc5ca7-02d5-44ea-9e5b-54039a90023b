# 故障排除指南

## 问题：字幕文件没有保存到视频文件夹

如果您遇到程序运行成功但字幕文件没有保存的问题，请按照以下步骤进行排查：

### 1. 使用调试版本程序

运行调试版本来获取详细的日志信息：

```bash
python debug_main.py
```

调试版本会：
- 在控制台显示详细日志
- 生成 `debug.log` 文件记录所有操作
- 显示更多的错误信息和处理步骤

### 2. 检查常见问题

#### 2.1 文件夹权限问题
- **问题**: 程序无法在目标文件夹中创建文件
- **解决方案**: 
  - 确保选择的文件夹有写入权限
  - 尝试选择其他文件夹（如桌面）进行测试
  - 以管理员身份运行程序

#### 2.2 磁盘空间不足
- **问题**: 磁盘空间不足导致文件创建失败
- **解决方案**: 
  - 检查磁盘剩余空间
  - 清理不必要的文件
  - 选择其他磁盘位置

#### 2.3 API调用失败
- **问题**: Gemini API调用失败，导致没有字幕内容可保存
- **解决方案**: 
  - 检查API密钥是否正确
  - 检查网络连接
  - 查看日志中的API错误信息

#### 2.4 音频提取失败
- **问题**: 视频文件无法提取音频
- **解决方案**: 
  - 确认视频文件完整且未损坏
  - 确认视频文件包含音轨
  - 尝试使用其他视频文件测试

### 3. 运行测试脚本

#### 3.1 基础功能测试
```bash
python test_installation.py
```
验证所有依赖是否正确安装。

#### 3.2 字幕生成测试
```bash
python test_subtitle_generation.py
```
测试SRT文件生成和保存功能。

#### 3.3 模拟完整流程测试
```bash
python test_mock_subtitle.py
```
模拟完整的字幕生成流程，不需要API调用。

### 4. 手动验证步骤

#### 4.1 检查视频文件
1. 确认选择的文件夹中确实包含支持的视频文件
2. 支持的格式：`.mp4`, `.avi`, `.mov`, `.mkv`, `.wmv`, `.flv`, `.webm`, `.m4v`
3. 确认视频文件没有损坏

#### 4.2 检查API设置
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 确认API密钥有效且未过期
3. 检查API使用配额

#### 4.3 检查文件系统
1. 确认目标文件夹存在且可访问
2. 尝试手动在该文件夹中创建文件
3. 检查文件夹路径中是否包含特殊字符

### 5. 常见错误信息及解决方案

#### "音频文件过大"
- **原因**: 音频文件超过20MB限制
- **解决方案**: 
  - 使用较短的视频文件
  - 压缩视频文件
  - 分段处理长视频

#### "API密钥错误"
- **原因**: API密钥无效或格式错误
- **解决方案**: 
  - 重新获取API密钥
  - 检查密钥是否完整复制
  - 确认密钥没有额外的空格

#### "网络连接失败"
- **原因**: 无法连接到Google服务
- **解决方案**: 
  - 检查网络连接
  - 尝试使用VPN
  - 检查防火墙设置

#### "权限被拒绝"
- **原因**: 无法写入目标文件夹
- **解决方案**: 
  - 以管理员身份运行程序
  - 更改文件夹权限
  - 选择其他文件夹

### 6. 获取帮助

如果以上步骤都无法解决问题，请：

1. **收集信息**:
   - 运行 `python debug_main.py` 获取详细日志
   - 记录具体的错误信息
   - 记录使用的视频文件信息

2. **检查日志文件**:
   - 查看 `debug.log` 文件
   - 找到具体的错误信息
   - 记录失败的步骤

3. **尝试简化测试**:
   - 使用单个小视频文件测试
   - 使用简单的文件夹路径（如桌面）
   - 确保网络连接稳定

### 7. 预防措施

为了避免问题发生，建议：

1. **选择合适的视频文件**:
   - 文件大小适中（建议小于100MB）
   - 确保包含音轨
   - 使用常见格式（MP4推荐）

2. **选择合适的文件夹**:
   - 避免使用系统文件夹
   - 确保有足够的磁盘空间
   - 路径中避免特殊字符

3. **网络环境**:
   - 确保网络连接稳定
   - 有足够的网络带宽
   - 能够访问Google服务

4. **API使用**:
   - 定期检查API配额
   - 保持API密钥的安全性
   - 了解API的使用限制

### 8. 联系支持

如果问题仍然存在，请提供以下信息：
- 操作系统版本
- Python版本
- 错误日志内容
- 使用的视频文件信息
- 具体的操作步骤

这将帮助更快地诊断和解决问题。
