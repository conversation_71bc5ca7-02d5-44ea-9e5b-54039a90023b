# 视频字幕自动生成器 - 使用说明

## 功能介绍

这是一个基于Python的桌面应用程序，可以自动为视频文件生成中英双语字幕。程序使用Google Gemini API进行语音识别和翻译，支持多种常见视频格式。

## 主要特性

- 🎥 支持多种视频格式（MP4, AVI, MOV, MKV等）
- 🎯 自动提取视频音频
- 🤖 使用Google Gemini AI进行语音转文字
- 🌐 自动生成中英双语字幕
- 📝 标准SRT字幕格式输出
- 🖥️ 友好的图形用户界面
- 📊 实时处理进度显示

## 安装步骤

### 1. 环境要求

- Python 3.7 或更高版本
- Windows/macOS/Linux 操作系统
- 稳定的网络连接（用于API调用）

### 2. 安装依赖

```bash
# 方法1：使用requirements.txt（推荐）
pip install -r requirements.txt

# 方法2：手动安装
pip install google-generativeai moviepy tkinter-tooltip Pillow
```

### 3. 获取API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的Google账户
3. 点击"Create API Key"创建新的API密钥
4. 复制生成的API密钥

## 使用方法

### 1. 启动程序

```bash
python main.py
```

### 2. 配置设置

1. **输入API密钥**：在"Gemini API密钥"输入框中粘贴您的API密钥
2. **选择文件夹**：点击"浏览"按钮选择包含视频文件的文件夹

### 3. 开始处理

1. 点击"开始生成字幕"按钮
2. 程序将自动处理文件夹中的所有视频文件
3. 处理进度和日志会实时显示在界面上
4. 完成后，字幕文件将保存在原视频文件夹中

## 支持的文件格式

### 视频格式
- MP4
- AVI
- MOV
- MKV
- WMV
- FLV
- WebM
- M4V

### 输出格式
- SRT字幕文件（UTF-8编码）
- 中英双语显示格式

## 注意事项

### API使用限制
- 单个音频文件大小限制：20MB
- 需要有效的Google账户和API密钥
- API调用可能产生费用（请查看Google AI定价）

### 文件处理
- 程序会为每个视频文件创建对应的.srt字幕文件
- 如果视频没有音轨，将跳过处理
- 处理时间取决于视频长度和网络速度

### 性能优化
- 建议一次处理的视频文件不要过多
- 确保网络连接稳定
- 处理大文件时请耐心等待

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查API密钥是否正确
   - 确认API密钥有效且未过期
   - 检查Google AI服务是否可用

2. **音频提取失败**
   - 确认视频文件完整且未损坏
   - 检查视频是否包含音轨
   - 尝试使用其他视频文件测试

3. **网络连接问题**
   - 检查网络连接是否稳定
   - 确认可以访问Google服务
   - 尝试使用VPN（如果需要）

4. **依赖安装问题**
   - 确认Python版本符合要求
   - 重新安装依赖包
   - 检查pip版本是否最新

### 错误日志

程序运行时的详细日志会显示在界面的"处理日志"区域，包括：
- 处理进度信息
- 错误详情
- API调用状态
- 文件处理结果

## 技术支持

如果遇到问题，请：
1. 查看程序日志中的错误信息
2. 检查网络连接和API密钥
3. 确认视频文件格式和完整性
4. 重启程序重试

## 版本信息

- 当前版本：1.0.0
- 最后更新：2024年
- 基于：Google Gemini API, MoviePy, Tkinter

## 许可证

本项目仅供学习和个人使用。使用Google Gemini API需要遵守Google的服务条款。
