"""
配置文件 - 存储应用程序的配置信息
"""
import os

# Gemini API配置
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']

# 支持的音频格式
SUPPORTED_AUDIO_FORMATS = ['.mp3', '.wav', '.aac', '.m4a', '.ogg']

# 临时文件夹
TEMP_DIR = 'temp_audio'

# 应用程序设置
APP_TITLE = "视频字幕自动生成器"
APP_VERSION = "1.0.0"
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600

# Gemini API设置
GEMINI_MODEL = "gemini-1.5-flash"
MAX_FILE_SIZE_MB = 20  # Gemini API文件大小限制
AUDIO_COMPRESSION_ENABLED = True  # 是否启用音频压缩
AUDIO_SAMPLE_RATE = 16000  # 音频采样率（降低以减小文件大小）
AUDIO_BITRATE = "32k"  # 音频比特率（进一步降低）
MAX_AUDIO_DURATION = 300  # 最大音频时长（秒），超过则分段处理
NETWORK_TIMEOUT = 60  # 网络请求超时时间（秒）
UPLOAD_CHUNK_SIZE = 1024 * 1024  # 上传块大小（1MB）

# 字幕设置
SRT_ENCODING = 'utf-8'
DEFAULT_SUBTITLE_DURATION = 3  # 默认字幕持续时间（秒）
