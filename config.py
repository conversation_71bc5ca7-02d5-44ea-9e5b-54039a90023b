"""
配置文件 - 存储应用程序的配置信息
"""
import os

# API配置
DEFAULT_API_KEY = ''  # 默认API密钥（将从配置文件读取）
API_KEY_FILE = 'api_key.txt'  # API密钥保存文件


def save_api_key(api_key: str):
    """保存API密钥到文件"""
    try:
        with open(API_KEY_FILE, 'w', encoding='utf-8') as f:
            f.write(api_key.strip())
        return True
    except Exception as e:
        print(f"保存API密钥失败: {e}")
        return False


def load_api_key() -> str:
    """从文件加载API密钥"""
    try:
        if os.path.exists(API_KEY_FILE):
            with open(API_KEY_FILE, 'r', encoding='utf-8') as f:
                return f.read().strip()
    except Exception as e:
        print(f"加载API密钥失败: {e}")
    return DEFAULT_API_KEY

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']

# 支持的音频格式
SUPPORTED_AUDIO_FORMATS = ['.mp3', '.wav', '.aac', '.m4a', '.ogg']

# 临时文件夹
TEMP_DIR = 'temp_audio'

# 应用程序设置
APP_TITLE = "视频字幕自动生成器 (DeepSeek版)"
APP_VERSION = "2.0.0"
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600

# DeepSeek API设置
DEEPSEEK_API_BASE = "https://api.deepseek.com/v1"
DEEPSEEK_MODEL = "deepseek-chat"
MAX_TOKENS = 4000  # 最大token数
TEMPERATURE = 0.1  # 温度参数，较低值使输出更确定

# 音频处理设置
AUDIO_COMPRESSION_ENABLED = True  # 是否启用音频压缩
AUDIO_SAMPLE_RATE = 16000  # 音频采样率
AUDIO_BITRATE = "32k"  # 音频比特率
MAX_AUDIO_DURATION = 600  # 最大音频时长（秒），10分钟
AUDIO_SEGMENT_DURATION = 60  # 音频分段时长（秒），1分钟一段

# 网络设置
NETWORK_TIMEOUT = 30  # 网络请求超时时间（秒）
MAX_RETRIES = 3  # 最大重试次数

# 字幕设置
SRT_ENCODING = 'utf-8'
DEFAULT_SUBTITLE_DURATION = 3  # 默认字幕持续时间（秒）
