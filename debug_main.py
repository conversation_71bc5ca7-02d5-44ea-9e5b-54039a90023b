#!/usr/bin/env python3
"""
调试版本的主程序
添加了详细的日志输出来帮助诊断问题
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging

# 设置详细的日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui import SubtitleGeneratorUI
    import config
except ImportError as e:
    logging.error(f"导入模块失败: {e}")
    sys.exit(1)


class DebugSubtitleGeneratorUI(SubtitleGeneratorUI):
    """调试版本的UI，添加更多日志输出"""
    
    def update_progress(self, message):
        """更新进度显示并记录日志"""
        logging.info(f"进度更新: {message}")
        super().update_progress(message)
    
    def process_videos(self):
        """处理视频的后台线程（调试版本）"""
        try:
            api_key = self.api_key_var.get().strip()
            folder_path = self.folder_var.get().strip()
            
            logging.info(f"开始处理视频")
            logging.info(f"API密钥长度: {len(api_key)}")
            logging.info(f"文件夹路径: {folder_path}")
            
            # 检查文件夹是否存在
            if not os.path.exists(folder_path):
                error_msg = f"文件夹不存在: {folder_path}"
                logging.error(error_msg)
                self.update_progress(error_msg)
                return
            
            # 检查文件夹权限
            if not os.access(folder_path, os.R_OK):
                error_msg = f"无法读取文件夹: {folder_path}"
                logging.error(error_msg)
                self.update_progress(error_msg)
                return
                
            if not os.access(folder_path, os.W_OK):
                error_msg = f"无法写入文件夹: {folder_path}"
                logging.error(error_msg)
                self.update_progress(error_msg)
                return
            
            # 初始化字幕生成器
            self.update_progress("初始化Gemini API...")
            
            from subtitle_generator import SubtitleGenerator
            self.generator = SubtitleGenerator(api_key)
            logging.info("字幕生成器初始化成功")
            
            # 查找视频文件
            from pathlib import Path
            folder = Path(folder_path)
            video_files = []
            
            logging.info(f"搜索视频文件，支持的格式: {config.SUPPORTED_VIDEO_FORMATS}")
            
            for ext in config.SUPPORTED_VIDEO_FORMATS:
                found_files = list(folder.glob(f"*{ext}"))
                found_files_upper = list(folder.glob(f"*{ext.upper()}"))
                video_files.extend(found_files)
                video_files.extend(found_files_upper)
                logging.info(f"格式 {ext}: 找到 {len(found_files)} 个文件")
                logging.info(f"格式 {ext.upper()}: 找到 {len(found_files_upper)} 个文件")
            
            if not video_files:
                error_msg = "未找到支持的视频文件"
                logging.warning(error_msg)
                self.update_progress(error_msg)
                return
            
            logging.info(f"总共找到 {len(video_files)} 个视频文件")
            for i, video_file in enumerate(video_files):
                logging.info(f"  {i+1}. {video_file}")
            
            # 处理文件夹
            self.update_progress("开始处理视频文件...")
            success_count, total_count = self.generator.process_folder(
                folder_path, 
                progress_callback=self.update_progress
            )
            
            # 完成处理
            if self.processing:  # 检查是否被用户停止
                result_msg = f"处理完成！成功: {success_count}/{total_count}"
                logging.info(result_msg)
                self.update_progress(result_msg)
                messagebox.showinfo("完成", f"字幕生成完成！\n成功处理: {success_count}/{total_count} 个视频")
            
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self.update_progress(error_msg)
            messagebox.showerror("错误", error_msg)
        
        finally:
            # 重置UI状态
            self.processing = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            if self.processing:
                self.update_progress("就绪")


def main():
    """主函数"""
    logging.info(f"启动调试版本 {config.APP_TITLE} v{config.APP_VERSION}")
    
    try:
        # 创建并运行应用程序
        app = DebugSubtitleGeneratorUI()
        app.root.title(f"{config.APP_TITLE} - 调试版本")
        app.run()
        
    except KeyboardInterrupt:
        logging.info("程序被用户中断")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"程序运行时发生错误: {str(e)}"
        logging.error(error_msg, exc_info=True)
        
        # 尝试显示GUI错误消息
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("运行错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
