#!/usr/bin/env python3
"""
DeepSeek版本视频字幕自动生成器
主程序入口

作者: AI Assistant
版本: 2.0.0
描述: 使用DeepSeek API自动为视频生成中英双语字幕的桌面应用程序
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from deepseek_ui import DeepSeekSubtitleGeneratorUI
    import config
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的文件都在同一目录下")
    sys.exit(1)


def check_dependencies():
    """检查必要的依赖是否已安装"""
    missing_deps = []
    
    try:
        import openai
    except ImportError:
        missing_deps.append("openai")
    
    try:
        import moviepy
    except ImportError:
        missing_deps.append("moviepy")
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    if missing_deps:
        error_msg = f"""
缺少必要的依赖包：{', '.join(missing_deps)}

请运行以下命令安装：
pip install {' '.join(missing_deps)}

或者运行：
pip install -r requirements.txt
        """
        print(error_msg)
        
        # 尝试显示GUI错误消息
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("依赖错误", error_msg)
        except:
            pass
        
        return False
    
    return True


def show_welcome_message():
    """显示欢迎信息"""
    welcome_text = f"""
欢迎使用 {config.APP_TITLE}！

版本: {config.APP_VERSION}
API提供商: DeepSeek

主要特性：
✓ 使用DeepSeek AI进行智能语音识别和翻译
✓ 支持多种视频格式
✓ 生成中英双语字幕
✓ 国内可直接访问，无需VPN
✓ API密钥自动保存功能

使用步骤：
1. 获取DeepSeek API密钥
2. 在程序中输入并保存API密钥
3. 选择包含视频文件的文件夹
4. 开始生成字幕

注意事项：
- 确保视频文件包含音轨
- 建议单个视频不超过10分钟
- API使用可能产生费用

准备开始使用吗？
    """
    
    try:
        root = tk.Tk()
        root.withdraw()
        result = messagebox.askquestion("欢迎", welcome_text, icon='question')
        root.destroy()
        return result == 'yes'
    except:
        print(welcome_text)
        return True


def main():
    """主函数"""
    print(f"启动 {config.APP_TITLE} v{config.APP_VERSION}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 显示欢迎信息
    if not show_welcome_message():
        print("用户取消启动")
        sys.exit(0)
    
    try:
        # 创建并运行应用程序
        app = DeepSeekSubtitleGeneratorUI()
        app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"程序运行时发生错误: {str(e)}"
        print(error_msg)
        
        # 尝试显示GUI错误消息
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("运行错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
