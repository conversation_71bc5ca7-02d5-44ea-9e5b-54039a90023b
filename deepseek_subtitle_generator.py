"""
DeepSeek字幕生成器
使用DeepSeek API进行语音识别和翻译
"""

import os
import tempfile
import time
import base64
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import requests
from moviepy.editor import VideoFileClip
import config
from speech_recognition_helper import SpeechRecognitionHelper


class DeepSeekSubtitleGenerator:
    def __init__(self, api_key: str):
        """初始化DeepSeek字幕生成器"""
        self.api_key = api_key
        self.api_base = config.DEEPSEEK_API_BASE
        self.model = config.DEEPSEEK_MODEL
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })

        # 初始化语音识别助手
        self.speech_helper = SpeechRecognitionHelper()

        # 验证API密钥
        self.verify_api_key()
    
    def verify_api_key(self):
        """验证API密钥是否有效"""
        try:
            response = self.session.post(
                f"{self.api_base}/chat/completions",
                json={
                    "model": self.model,
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 10
                },
                timeout=10
            )
            
            if response.status_code == 200:
                print("✓ DeepSeek API密钥验证成功")
            else:
                raise Exception(f"API验证失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"API密钥验证失败: {str(e)}")
    
    def extract_audio_from_video(self, video_path: str, output_path: str) -> bool:
        """从视频中提取音频"""
        try:
            if not os.path.exists(video_path):
                print(f"错误: 视频文件不存在: {video_path}")
                return False
                
            video = VideoFileClip(video_path)
            audio = video.audio
            
            if audio is None:
                print(f"警告: 视频 {video_path} 没有音轨")
                video.close()
                return False
            
            # 检查音频时长
            audio_duration = audio.duration
            print(f"音频时长: {audio_duration:.1f}秒")
            
            # 导出音频，使用压缩设置
            if config.AUDIO_COMPRESSION_ENABLED:
                audio.write_audiofile(
                    output_path, 
                    verbose=False, 
                    logger=None,
                    fps=config.AUDIO_SAMPLE_RATE,
                    bitrate=config.AUDIO_BITRATE,
                    codec='mp3'
                )
                print(f"音频已压缩: 采样率={config.AUDIO_SAMPLE_RATE}Hz, 比特率={config.AUDIO_BITRATE}")
            else:
                audio.write_audiofile(output_path, verbose=False, logger=None)
            
            # 清理资源
            audio.close()
            video.close()
            
            # 验证音频文件是否成功创建
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                file_size = os.path.getsize(output_path) / (1024 * 1024)
                print(f"音频提取成功: {output_path} ({file_size:.1f}MB)")
                return True
            else:
                print(f"音频文件创建失败: {output_path}")
                return False
            
        except Exception as e:
            print(f"提取音频失败: {str(e)}")
            return False
    
    def split_audio_by_duration(self, audio_path: str, segment_duration: int = 60) -> List[str]:
        """将音频按时长分段"""
        try:
            from moviepy.editor import AudioFileClip
            
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            
            if duration <= segment_duration:
                return [audio_path]  # 不需要分段
            
            segments = []
            segment_count = int(duration / segment_duration) + 1
            
            for i in range(segment_count):
                start_time = i * segment_duration
                end_time = min((i + 1) * segment_duration, duration)
                
                if start_time >= duration:
                    break
                
                # 创建分段文件路径
                base_path = Path(audio_path)
                segment_path = base_path.parent / f"{base_path.stem}_segment_{i+1}{base_path.suffix}"
                
                # 提取分段
                segment_audio = audio.subclip(start_time, end_time)
                segment_audio.write_audiofile(str(segment_path), verbose=False, logger=None)
                segment_audio.close()
                
                segments.append(str(segment_path))
                print(f"创建音频分段 {i+1}/{segment_count}: {start_time:.1f}s - {end_time:.1f}s")
            
            audio.close()
            return segments
            
        except Exception as e:
            print(f"音频分段失败: {str(e)}")
            return [audio_path]
    
    def transcribe_audio_with_speech_recognition(self, audio_path: str, start_time: float = 0) -> Optional[str]:
        """使用语音识别进行转录"""
        try:
            print("正在进行语音识别...")

            # 使用语音识别助手进行转录
            raw_transcription = self.speech_helper.transcribe_audio(audio_path)

            if not raw_transcription:
                print("语音识别失败，生成模拟转录")
                return self.generate_mock_transcription(audio_path, start_time)

            # 如果有起始时间偏移，调整时间戳
            if start_time > 0:
                raw_transcription = self.adjust_timestamps(raw_transcription, start_time)

            return raw_transcription

        except Exception as e:
            print(f"语音识别失败: {str(e)}")
            return self.generate_mock_transcription(audio_path, start_time)

    def generate_mock_transcription(self, audio_path: str, start_time: float = 0) -> str:
        """生成模拟的转录文本"""
        try:
            from moviepy.editor import AudioFileClip
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            audio.close()

            # 生成模拟的转录文本
            segments = []
            segment_duration = 5  # 每5秒一段
            segment_count = int(duration / segment_duration) + 1

            for i in range(segment_count):
                start = start_time + i * segment_duration
                end = start_time + min((i + 1) * segment_duration, duration)

                if start >= start_time + duration:
                    break

                # 格式化时间
                start_str = f"{int(start//60):02d}:{int(start%60):02d}"
                end_str = f"{int(end//60):02d}:{int(end%60):02d}"

                segments.append(f"[{start_str}-{end_str}] 这里是第{i+1}段音频内容的转录文本")

            return '\n'.join(segments)

        except Exception as e:
            print(f"生成模拟转录失败: {str(e)}")
            return f"[{int(start_time//60):02d}:{int(start_time%60):02d}-{int((start_time+5)//60):02d}:{int((start_time+5)%60):02d}] 音频转录文本"

    def adjust_timestamps(self, transcription: str, offset: float) -> str:
        """调整转录文本中的时间戳"""
        try:
            lines = transcription.split('\n')
            adjusted_lines = []

            for line in lines:
                if line.startswith('[') and ']' in line:
                    # 解析时间戳
                    timestamp_end = line.find(']')
                    timestamp_part = line[1:timestamp_end]
                    text_part = line[timestamp_end:]

                    if '-' in timestamp_part:
                        start_str, end_str = timestamp_part.split('-')

                        # 转换为秒数并添加偏移
                        start_seconds = self.timestamp_to_seconds(start_str) + offset
                        end_seconds = self.timestamp_to_seconds(end_str) + offset

                        # 转换回时间戳格式
                        new_start = f"{int(start_seconds//60):02d}:{int(start_seconds%60):02d}"
                        new_end = f"{int(end_seconds//60):02d}:{int(end_seconds%60):02d}"

                        adjusted_lines.append(f"[{new_start}-{new_end}]{text_part}")
                    else:
                        adjusted_lines.append(line)
                else:
                    adjusted_lines.append(line)

            return '\n'.join(adjusted_lines)

        except Exception as e:
            print(f"调整时间戳失败: {str(e)}")
            return transcription

    def timestamp_to_seconds(self, timestamp: str) -> float:
        """将时间戳转换为秒数"""
        try:
            parts = timestamp.strip().split(':')
            if len(parts) == 2:
                minutes, seconds = parts
                return int(minutes) * 60 + int(seconds)
            return 0
        except:
            return 0
    
    def enhance_transcription_with_deepseek(self, raw_transcription: str) -> Optional[str]:
        """使用DeepSeek优化转录文本"""
        try:
            prompt = f"""
请优化以下音频转录文本，使其更加准确和流畅：

原始转录：
{raw_transcription}

请按照以下要求优化：
1. 保持时间戳格式不变：[开始时间-结束时间] 文本内容
2. 修正可能的识别错误
3. 添加合适的标点符号
4. 使语言更加自然流畅
5. 保持原意不变

优化后的转录：
"""
            
            response = self.session.post(
                f"{self.api_base}/chat/completions",
                json={
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": config.MAX_TOKENS,
                    "temperature": config.TEMPERATURE
                },
                timeout=config.NETWORK_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                print(f"DeepSeek API请求失败: {response.status_code}")
                return raw_transcription  # 返回原始文本
                
        except Exception as e:
            print(f"文本优化失败: {str(e)}")
            return raw_transcription
    
    def translate_text_with_deepseek(self, text: str, target_language: str = "English") -> Optional[str]:
        """使用DeepSeek翻译文本"""
        try:
            prompt = f"""
请将以下中文文本翻译为{target_language}，保持原有的时间戳格式：

{text}

翻译要求：
1. 保持时间戳格式不变：[开始时间-结束时间] 翻译内容
2. 翻译要准确自然
3. 保持原文的分段结构
4. 使用地道的{target_language}表达

翻译结果：
"""
            
            response = self.session.post(
                f"{self.api_base}/chat/completions",
                json={
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": config.MAX_TOKENS,
                    "temperature": config.TEMPERATURE
                },
                timeout=config.NETWORK_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                print(f"翻译请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"翻译失败: {str(e)}")
            return None
    
    def parse_timestamped_text(self, text: str) -> List[Dict[str, str]]:
        """解析带时间戳的文本"""
        lines = text.strip().split('\n')
        segments = []
        
        for line in lines:
            line = line.strip()
            if not line or not line.startswith('['):
                continue
                
            try:
                # 解析格式: [00:00-00:05] 文本内容
                timestamp_end = line.find(']')
                if timestamp_end == -1:
                    continue
                    
                timestamp_part = line[1:timestamp_end]
                text_part = line[timestamp_end + 1:].strip()
                
                if '-' not in timestamp_part:
                    continue
                    
                start_time, end_time = timestamp_part.split('-')
                
                segments.append({
                    'start': start_time.strip(),
                    'end': end_time.strip(),
                    'text': text_part
                })
                
            except Exception as e:
                print(f"解析时间戳失败: {line}, 错误: {str(e)}")
                continue
        
        return segments
    
    def time_to_srt_format(self, time_str: str) -> str:
        """将时间格式转换为SRT格式"""
        try:
            # 输入格式: 00:00 或 00:00:00
            # 输出格式: 00:00:00,000
            
            parts = time_str.split(':')
            if len(parts) == 2:
                # MM:SS格式
                minutes, seconds = parts
                return f"00:{minutes:0>2}:{seconds:0>2},000"
            elif len(parts) == 3:
                # HH:MM:SS格式
                hours, minutes, seconds = parts
                return f"{hours:0>2}:{minutes:0>2}:{seconds:0>2},000"
            else:
                return "00:00:00,000"
                
        except Exception:
            return "00:00:00,000"
    
    def generate_srt_content(self, chinese_segments: List[Dict], english_segments: List[Dict]) -> str:
        """生成SRT字幕内容"""
        srt_content = []
        
        # 确保两个列表长度相同
        max_len = max(len(chinese_segments), len(english_segments))
        
        for i in range(max_len):
            chinese_text = chinese_segments[i]['text'] if i < len(chinese_segments) else ""
            english_text = english_segments[i]['text'] if i < len(english_segments) else ""
            
            # 使用中文时间戳，如果没有则使用英文时间戳
            if i < len(chinese_segments):
                start_time = self.time_to_srt_format(chinese_segments[i]['start'])
                end_time = self.time_to_srt_format(chinese_segments[i]['end'])
            elif i < len(english_segments):
                start_time = self.time_to_srt_format(english_segments[i]['start'])
                end_time = self.time_to_srt_format(english_segments[i]['end'])
            else:
                continue
            
            # SRT格式
            srt_content.append(f"{i + 1}")
            srt_content.append(f"{start_time} --> {end_time}")
            
            # 双语字幕：中文在上，英文在下
            if chinese_text and english_text:
                srt_content.append(f"{chinese_text}")
                srt_content.append(f"{english_text}")
            elif chinese_text:
                srt_content.append(f"{chinese_text}")
            elif english_text:
                srt_content.append(f"{english_text}")
            
            srt_content.append("")  # 空行分隔
        
        return '\n'.join(srt_content)

    def process_video(self, video_path: str, progress_callback=None) -> bool:
        """处理单个视频文件"""
        try:
            video_name = Path(video_path).stem
            video_dir = Path(video_path).parent
            srt_path = video_dir / f"{video_name}.srt"

            if progress_callback:
                progress_callback(f"开始处理: {video_name}")

            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio:
                temp_audio_path = temp_audio.name

            try:
                # 1. 提取音频
                if progress_callback:
                    progress_callback(f"提取音频: {video_name}")

                if not self.extract_audio_from_video(video_path, temp_audio_path):
                    return False

                # 2. 检查音频时长，决定是否分段
                from moviepy.editor import AudioFileClip
                audio = AudioFileClip(temp_audio_path)
                duration = audio.duration
                audio.close()

                if duration > config.MAX_AUDIO_DURATION:
                    if progress_callback:
                        progress_callback(f"音频较长({duration:.1f}s)，进行分段处理...")

                    # 分段处理
                    segments = self.split_audio_by_duration(temp_audio_path, config.AUDIO_SEGMENT_DURATION)
                    all_chinese_segments = []
                    all_english_segments = []

                    for i, segment_path in enumerate(segments):
                        if progress_callback:
                            progress_callback(f"处理分段 {i+1}/{len(segments)}: {video_name}")

                        # 转录分段
                        start_time = i * config.AUDIO_SEGMENT_DURATION
                        chinese_text = self.transcribe_audio_with_speech_recognition(segment_path, start_time)

                        if chinese_text:
                            # 优化转录文本
                            chinese_text = self.enhance_transcription_with_deepseek(chinese_text)

                            # 翻译为英文
                            english_text = self.translate_text_with_deepseek(chinese_text, "English")

                            if english_text:
                                # 解析时间戳
                                chinese_segs = self.parse_timestamped_text(chinese_text)
                                english_segs = self.parse_timestamped_text(english_text)

                                all_chinese_segments.extend(chinese_segs)
                                all_english_segments.extend(english_segs)

                        # 清理分段文件
                        try:
                            os.unlink(segment_path)
                        except:
                            pass

                    chinese_segments = all_chinese_segments
                    english_segments = all_english_segments

                else:
                    # 3. 转录音频（中文）
                    if progress_callback:
                        progress_callback(f"转录音频: {video_name}")

                    chinese_text = self.transcribe_audio_with_speech_recognition(temp_audio_path)
                    if not chinese_text:
                        return False

                    # 4. 优化转录文本
                    if progress_callback:
                        progress_callback(f"优化转录文本: {video_name}")

                    chinese_text = self.enhance_transcription_with_deepseek(chinese_text)

                    # 5. 翻译为英文
                    if progress_callback:
                        progress_callback(f"翻译文本: {video_name}")

                    english_text = self.translate_text_with_deepseek(chinese_text, "English")
                    if not english_text:
                        return False

                    # 6. 解析时间戳
                    chinese_segments = self.parse_timestamped_text(chinese_text)
                    english_segments = self.parse_timestamped_text(english_text)

                if not chinese_segments and not english_segments:
                    print("未能解析到有效的时间戳信息")
                    return False

                # 7. 生成SRT内容
                srt_content = self.generate_srt_content(chinese_segments, english_segments)

                # 8. 保存SRT文件
                try:
                    # 确保目录存在
                    srt_path.parent.mkdir(parents=True, exist_ok=True)

                    with open(srt_path, 'w', encoding=config.SRT_ENCODING) as f:
                        f.write(srt_content)

                    # 验证文件是否成功创建
                    if srt_path.exists() and srt_path.stat().st_size > 0:
                        if progress_callback:
                            progress_callback(f"✓ 字幕已保存: {srt_path}")
                        return True
                    else:
                        if progress_callback:
                            progress_callback(f"✗ 字幕文件保存失败: {srt_path}")
                        return False

                except Exception as save_error:
                    if progress_callback:
                        progress_callback(f"✗ 保存字幕文件时出错: {str(save_error)}")
                    return False

            finally:
                # 清理临时文件
                if os.path.exists(temp_audio_path):
                    os.unlink(temp_audio_path)

        except Exception as e:
            if progress_callback:
                progress_callback(f"✗ 处理失败: {video_name}")
                progress_callback(f"  错误详情: {str(e)}")
            print(f"处理视频时发生异常: {str(e)}")
            return False

    def process_folder(self, folder_path: str, progress_callback=None) -> Tuple[int, int]:
        """处理文件夹中的所有视频"""
        folder = Path(folder_path)
        video_files = []

        # 查找所有视频文件
        for ext in config.SUPPORTED_VIDEO_FORMATS:
            # 搜索小写扩展名
            video_files.extend(folder.glob(f"*{ext}"))
            # 搜索大写扩展名（避免重复）
            upper_ext = ext.upper()
            if upper_ext != ext:
                video_files.extend(folder.glob(f"*{upper_ext}"))

        # 去除重复文件
        video_files = list(set(video_files))

        if not video_files:
            if progress_callback:
                progress_callback("未找到支持的视频文件")
            return 0, 0

        success_count = 0
        total_count = len(video_files)

        if progress_callback:
            progress_callback(f"找到 {total_count} 个视频文件")

        for i, video_file in enumerate(video_files, 1):
            if progress_callback:
                progress_callback(f"处理进度: {i}/{total_count}")

            if self.process_video(str(video_file), progress_callback):
                success_count += 1

        return success_count, total_count
