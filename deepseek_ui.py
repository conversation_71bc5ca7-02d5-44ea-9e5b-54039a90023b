"""
DeepSeek版本的GUI界面模块
支持API密钥记忆功能
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
import config
from deepseek_subtitle_generator import DeepSeekSubtitleGenerator


class DeepSeekSubtitleGeneratorUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.generator = None
        self.processing = False
        
        # 加载保存的API密钥
        self.load_saved_api_key()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title(config.APP_TITLE)
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 可选：添加应用图标
            pass
        except:
            pass
    
    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # 标题和版本信息
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        ttk.Label(title_frame, text=config.APP_TITLE, 
                 font=("Arial", 14, "bold")).pack()
        ttk.Label(title_frame, text=f"版本 {config.APP_VERSION} - 使用DeepSeek API", 
                 font=("Arial", 9)).pack()
        
        # API密钥输入
        ttk.Label(main_frame, text="DeepSeek API密钥:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.api_key_var = tk.StringVar()
        self.api_key_entry = ttk.Entry(main_frame, textvariable=self.api_key_var, show="*", width=50)
        self.api_key_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # API密钥操作按钮框架
        api_button_frame = ttk.Frame(main_frame)
        api_button_frame.grid(row=1, column=2, padx=(5, 0), pady=(0, 5))
        
        ttk.Button(api_button_frame, text="保存", width=6,
                  command=self.save_api_key).pack(side=tk.LEFT)
        ttk.Button(api_button_frame, text="帮助", width=6,
                  command=self.show_api_help).pack(side=tk.LEFT, padx=(2, 0))
        
        # 文件夹选择
        ttk.Label(main_frame, text="视频文件夹:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.folder_var = tk.StringVar()
        self.folder_entry = ttk.Entry(main_frame, textvariable=self.folder_var, width=50)
        self.folder_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(main_frame, text="浏览", 
                  command=self.select_folder).grid(row=2, column=2, padx=(5, 0), pady=(0, 5))
        
        # 处理选项
        options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="5")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))
        
        self.compress_audio_var = tk.BooleanVar(value=config.AUDIO_COMPRESSION_ENABLED)
        ttk.Checkbutton(options_frame, text="启用音频压缩（推荐）", 
                       variable=self.compress_audio_var).pack(anchor=tk.W)
        
        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始生成字幕", 
                                      command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止", 
                                     command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.grid(row=1, column=0, pady=(5, 0))
        
        ttk.Button(log_button_frame, text="清除日志", 
                  command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_button_frame, text="保存日志", 
                  command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))
        
        # 显示欢迎信息
        self.log_message("=== DeepSeek视频字幕自动生成器 ===")
        self.log_message("1. 输入DeepSeek API密钥并点击'保存'")
        self.log_message("2. 选择包含视频文件的文件夹")
        self.log_message("3. 点击'开始生成字幕'")
        self.log_message("")
        self.log_message("注意：DeepSeek版本使用国内可访问的API，无需VPN")
        self.log_message("支持的视频格式：MP4, AVI, MOV, MKV, WMV, FLV, WebM, M4V")
        self.log_message("")
    
    def load_saved_api_key(self):
        """加载保存的API密钥"""
        try:
            saved_key = config.load_api_key()
            if saved_key:
                self.api_key_var.set(saved_key)
                self.log_message("✓ 已加载保存的API密钥")
        except Exception as e:
            self.log_message(f"加载API密钥失败: {str(e)}")
    
    def save_api_key(self):
        """保存API密钥"""
        api_key = self.api_key_var.get().strip()
        if not api_key:
            messagebox.showwarning("警告", "请先输入API密钥")
            return
        
        if config.save_api_key(api_key):
            self.log_message("✓ API密钥已保存")
            messagebox.showinfo("成功", "API密钥已保存，下次启动时会自动加载")
        else:
            messagebox.showerror("错误", "保存API密钥失败")
    
    def show_api_help(self):
        """显示API密钥帮助信息"""
        help_text = """
获取DeepSeek API密钥的步骤：

1. 访问 DeepSeek 官网: https://platform.deepseek.com/
2. 注册并登录您的账户
3. 进入API管理页面
4. 创建新的API密钥
5. 复制生成的API密钥并粘贴到上面的输入框中
6. 点击"保存"按钮保存密钥

注意：
- 请妥善保管您的API密钥
- 不要与他人分享您的API密钥
- DeepSeek API支持国内访问，无需VPN
- API使用可能产生费用，请查看DeepSeek定价

示例API密钥格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
        """
        messagebox.showinfo("API密钥帮助", help_text.strip())
    
    def select_folder(self):
        """选择视频文件夹"""
        folder = filedialog.askdirectory(title="选择包含视频文件的文件夹")
        if folder:
            self.folder_var.set(folder)
            self.log_message(f"选择文件夹: {folder}")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
    
    def save_log(self):
        """保存日志到文件"""
        try:
            log_content = self.log_text.get(1.0, tk.END)
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                messagebox.showinfo("成功", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")
    
    def update_status(self, message):
        """更新状态显示"""
        self.status_var.set(message)
        self.log_message(message)
    
    def validate_inputs(self):
        """验证输入"""
        api_key = self.api_key_var.get().strip()
        folder_path = self.folder_var.get().strip()
        
        if not api_key:
            messagebox.showerror("错误", "请输入DeepSeek API密钥")
            return False
        
        if not folder_path:
            messagebox.showerror("错误", "请选择视频文件夹")
            return False
        
        if not os.path.exists(folder_path):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return False
        
        return True
    
    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return
        
        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请等待完成")
            return
        
        # 启动处理线程
        self.processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        thread = threading.Thread(target=self.process_videos, daemon=True)
        thread.start()
    
    def stop_processing(self):
        """停止处理"""
        self.processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.update_status("已停止")
    
    def process_videos(self):
        """处理视频的后台线程"""
        try:
            api_key = self.api_key_var.get().strip()
            folder_path = self.folder_var.get().strip()
            
            # 更新音频压缩设置
            config.AUDIO_COMPRESSION_ENABLED = self.compress_audio_var.get()
            
            # 初始化DeepSeek字幕生成器
            self.update_status("初始化DeepSeek API...")
            try:
                self.generator = DeepSeekSubtitleGenerator(api_key)
                self.update_status("✓ DeepSeek API初始化成功")
            except Exception as e:
                error_msg = f"API初始化失败: {str(e)}"
                self.update_status(error_msg)
                messagebox.showerror("API错误", f"{error_msg}\n\n请检查：\n1. API密钥是否正确\n2. 网络连接是否正常\n3. API账户是否有效")
                return
            
            # 处理文件夹
            self.update_status("开始处理视频文件...")
            success_count, total_count = self.generator.process_folder(
                folder_path, 
                progress_callback=self.update_status
            )
            
            # 完成处理
            if self.processing:  # 检查是否被用户停止
                if success_count > 0:
                    self.update_status(f"✓ 处理完成！成功: {success_count}/{total_count}")
                    messagebox.showinfo("完成", f"字幕生成完成！\n成功处理: {success_count}/{total_count} 个视频\n\n字幕文件已保存到视频所在文件夹")
                else:
                    self.update_status(f"✗ 处理失败！成功: {success_count}/{total_count}")
                    messagebox.showwarning("处理失败", f"没有成功处理任何视频文件\n\n可能的原因：\n1. API调用失败\n2. 视频文件没有音轨\n3. API配额不足\n\n请查看日志了解详细信息")
            
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.update_status(error_msg)
            messagebox.showerror("错误", f"{error_msg}\n\n请尝试：\n1. 检查API密钥\n2. 检查网络连接\n3. 重启程序重试")
        
        finally:
            # 重置UI状态
            self.processing = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            if not self.processing:
                self.update_status("就绪")
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    app = DeepSeekSubtitleGeneratorUI()
    app.run()


if __name__ == "__main__":
    main()
