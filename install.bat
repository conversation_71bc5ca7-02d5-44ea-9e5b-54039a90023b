@echo off
chcp 65001 >nul
echo ========================================
echo 视频字幕自动生成器 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo.

echo 正在检查pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到pip
    echo 请确保pip已正确安装
    pause
    exit /b 1
)

echo 正在升级pip...
python -m pip install --upgrade pip
echo.

echo 正在安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 错误：依赖安装失败
    echo 请检查网络连接或手动安装依赖
    pause
    exit /b 1
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. 运行: python main.py
echo 2. 或者双击: run.bat
echo.
echo 首次使用请准备好Gemini API密钥
echo 获取地址：https://makersuite.google.com/app/apikey
echo.
pause
