@echo off
chcp 65001 >nul
echo ========================================
echo DeepSeek视频字幕自动生成器 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo.

echo 正在检查pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到pip
    echo 请确保pip已正确安装
    pause
    exit /b 1
)

echo 正在升级pip...
python -m pip install --upgrade pip
echo.

echo 正在安装基础依赖包...
pip install openai moviepy requests Pillow tkinter-tooltip
if errorlevel 1 (
    echo 错误：基础依赖安装失败
    pause
    exit /b 1
)

echo.
echo 正在安装语音识别依赖（可选）...
echo 注意：这些依赖较大，可能需要较长时间下载
echo.

echo 安装SpeechRecognition...
pip install SpeechRecognition
if errorlevel 1 (
    echo 警告：SpeechRecognition安装失败，将使用模拟转录
)

echo 安装Whisper（推荐，但文件较大）...
pip install openai-whisper
if errorlevel 1 (
    echo 警告：Whisper安装失败，将使用其他语音识别方法
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. 运行: python deepseek_main.py
echo 2. 或者双击: run_deepseek.bat
echo.
echo 首次使用请准备好DeepSeek API密钥
echo 获取地址：https://platform.deepseek.com/
echo.
echo 注意事项：
echo - DeepSeek API支持国内访问，无需VPN
echo - 如果语音识别依赖安装失败，程序仍可运行（使用模拟转录）
echo - 建议安装Whisper以获得更好的语音识别效果
echo.
pause
