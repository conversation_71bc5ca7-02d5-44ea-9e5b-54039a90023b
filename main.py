#!/usr/bin/env python3
"""
视频字幕自动生成器
主程序入口

作者: AI Assistant
版本: 1.0.0
描述: 使用Gemini API自动为视频生成中英双语字幕的桌面应用程序
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui import SubtitleGeneratorUI
    import config
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的文件都在同一目录下")
    sys.exit(1)


def check_dependencies():
    """检查必要的依赖是否已安装"""
    missing_deps = []
    
    try:
        import google.generativeai
    except ImportError:
        missing_deps.append("google-generativeai")
    
    try:
        import moviepy
    except ImportError:
        missing_deps.append("moviepy")
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    if missing_deps:
        error_msg = f"""
缺少必要的依赖包：{', '.join(missing_deps)}

请运行以下命令安装：
pip install {' '.join(missing_deps)}

或者运行：
pip install -r requirements.txt
        """
        print(error_msg)
        
        # 尝试显示GUI错误消息
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("依赖错误", error_msg)
        except:
            pass
        
        return False
    
    return True


def main():
    """主函数"""
    print(f"启动 {config.APP_TITLE} v{config.APP_VERSION}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # 创建并运行应用程序
        app = SubtitleGeneratorUI()
        app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"程序运行时发生错误: {str(e)}"
        print(error_msg)
        
        # 尝试显示GUI错误消息
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("运行错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
