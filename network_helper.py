"""
网络连接辅助工具
提供网络连接优化和代理支持
"""

import os
import socket
import time
import requests
from typing import Optional, Dict, Any
import google.generativeai as genai


class NetworkHelper:
    """网络连接辅助类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """设置网络会话"""
        # 设置超时
        self.session.timeout = 30
        
        # 设置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置User-Agent
        self.session.headers.update({
            'User-Agent': 'SubtitleGenerator/1.0'
        })
    
    def test_connection(self, host: str = "*******", port: int = 53, timeout: int = 5) -> bool:
        """测试网络连接"""
        try:
            socket.create_connection((host, port), timeout=timeout)
            return True
        except OSError:
            return False
    
    def test_google_api_access(self) -> bool:
        """测试Google API访问"""
        try:
            response = self.session.get(
                "https://generativelanguage.googleapis.com/",
                timeout=10
            )
            return response.status_code in [200, 404]  # 404也表示能访问到服务
        except Exception:
            return False
    
    def get_optimal_proxy(self) -> Optional[Dict[str, str]]:
        """获取最优代理设置（如果需要）"""
        # 这里可以添加代理检测逻辑
        # 目前返回None，表示不使用代理
        return None
    
    def configure_gemini_with_proxy(self, api_key: str, proxy: Optional[Dict[str, str]] = None):
        """配置Gemini API，支持代理"""
        if proxy:
            # 设置代理环境变量
            os.environ['HTTP_PROXY'] = proxy.get('http', '')
            os.environ['HTTPS_PROXY'] = proxy.get('https', '')
        
        genai.configure(api_key=api_key)
    
    def upload_with_retry(self, file_path: str, max_retries: int = 5) -> Optional[Any]:
        """带重试的文件上传"""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                print(f"尝试上传文件 (第 {attempt + 1}/{max_retries} 次)...")
                
                # 检查文件大小
                file_size = os.path.getsize(file_path) / (1024 * 1024)
                print(f"文件大小: {file_size:.1f}MB")
                
                if file_size > 15:  # 如果文件仍然很大，给出警告
                    print(f"⚠️  文件较大({file_size:.1f}MB)，上传可能需要较长时间")
                
                # 上传文件
                audio_file = genai.upload_file(file_path)
                print(f"✓ 文件上传成功 (尝试 {attempt + 1})")
                return audio_file
                
            except Exception as e:
                last_error = e
                error_msg = str(e)
                print(f"✗ 上传失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                
                # 根据错误类型决定是否继续重试
                if "10060" in error_msg or "timeout" in error_msg.lower():
                    # 网络超时错误，可以重试
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # 指数退避
                        print(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                elif "quota" in error_msg.lower() or "limit" in error_msg.lower():
                    # 配额或限制错误，不需要重试
                    print("API配额或限制错误，停止重试")
                    break
                elif "authentication" in error_msg.lower() or "key" in error_msg.lower():
                    # 认证错误，不需要重试
                    print("API密钥错误，停止重试")
                    break
                else:
                    # 其他错误，继续重试
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt
                        print(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
        
        print(f"所有上传尝试都失败了，最后错误: {last_error}")
        return None
    
    def generate_content_with_retry(self, model, prompt_and_file, max_retries: int = 3) -> Optional[Any]:
        """带重试的内容生成"""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                print(f"尝试生成内容 (第 {attempt + 1}/{max_retries} 次)...")
                response = model.generate_content(prompt_and_file)
                
                if response and response.text:
                    print(f"✓ 内容生成成功 (尝试 {attempt + 1})")
                    return response
                else:
                    print(f"✗ 生成的内容为空 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)
                        continue
                
            except Exception as e:
                last_error = e
                error_msg = str(e)
                print(f"✗ 内容生成失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
        
        print(f"所有生成尝试都失败了，最后错误: {last_error}")
        return None


def create_network_helper() -> NetworkHelper:
    """创建网络辅助工具实例"""
    return NetworkHelper()


def test_network_environment():
    """测试网络环境"""
    helper = NetworkHelper()
    
    print("测试网络环境...")
    
    # 测试基本连接
    if helper.test_connection():
        print("✓ 基本网络连接正常")
    else:
        print("✗ 基本网络连接失败")
        return False
    
    # 测试Google API访问
    if helper.test_google_api_access():
        print("✓ Google API访问正常")
        return True
    else:
        print("✗ Google API访问失败")
        print("建议：")
        print("1. 检查防火墙设置")
        print("2. 尝试使用VPN")
        print("3. 检查代理设置")
        return False


if __name__ == "__main__":
    test_network_environment()
