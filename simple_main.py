#!/usr/bin/env python3
"""
简化版本的字幕生成器
专门优化网络连接问题
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from network_helper import test_network_environment
    import config
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class SimpleSubtitleGeneratorUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.processing = False
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title(f"{config.APP_TITLE} - 简化版本")
        self.root.geometry("700x500")
        self.root.resizable(True, True)
    
    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="视频字幕自动生成器 - 简化版本", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 网络测试按钮
        ttk.Button(main_frame, text="测试网络连接", 
                  command=self.test_network).grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # API密钥输入
        ttk.Label(main_frame, text="Gemini API密钥:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.api_key_var = tk.StringVar(value=config.GEMINI_API_KEY)
        self.api_key_entry = ttk.Entry(main_frame, textvariable=self.api_key_var, show="*", width=50)
        self.api_key_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 文件夹选择
        ttk.Label(main_frame, text="视频文件夹:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        self.folder_var = tk.StringVar()
        self.folder_entry = ttk.Entry(main_frame, textvariable=self.folder_var, width=50)
        self.folder_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(main_frame, text="浏览", 
                  command=self.select_folder).grid(row=3, column=2, padx=(5, 0), pady=(0, 5))
        
        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始生成字幕", 
                                      command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止", 
                                     command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志", 
                  command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        # 显示使用提示
        self.log_message("=== 简化版本字幕生成器 ===")
        self.log_message("1. 首先点击'测试网络连接'检查网络状态")
        self.log_message("2. 输入有效的Gemini API密钥")
        self.log_message("3. 选择包含视频文件的文件夹")
        self.log_message("4. 点击'开始生成字幕'")
        self.log_message("")
        self.log_message("注意：此版本专门优化了网络连接问题")
        self.log_message("如果仍然遇到问题，请检查网络设置或使用VPN")
        self.log_message("")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
    
    def test_network(self):
        """测试网络连接"""
        self.log_message("开始测试网络连接...")
        
        def test_in_thread():
            try:
                if test_network_environment():
                    self.log_message("✓ 网络连接测试通过")
                    messagebox.showinfo("网络测试", "网络连接正常，可以使用字幕生成功能")
                else:
                    self.log_message("✗ 网络连接测试失败")
                    messagebox.showwarning("网络测试", 
                                         "网络连接存在问题\n\n建议：\n1. 检查网络连接\n2. 尝试使用VPN\n3. 检查防火墙设置")
            except Exception as e:
                error_msg = f"网络测试出错: {str(e)}"
                self.log_message(error_msg)
                messagebox.showerror("测试错误", error_msg)
        
        # 在后台线程中运行测试
        thread = threading.Thread(target=test_in_thread, daemon=True)
        thread.start()
    
    def select_folder(self):
        """选择视频文件夹"""
        folder = filedialog.askdirectory(title="选择包含视频文件的文件夹")
        if folder:
            self.folder_var.set(folder)
            self.log_message(f"选择文件夹: {folder}")
    
    def validate_inputs(self):
        """验证输入"""
        api_key = self.api_key_var.get().strip()
        folder_path = self.folder_var.get().strip()
        
        if not api_key:
            messagebox.showerror("错误", "请输入Gemini API密钥")
            return False
        
        if not folder_path:
            messagebox.showerror("错误", "请选择视频文件夹")
            return False
        
        if not os.path.exists(folder_path):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return False
        
        return True
    
    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return
        
        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请等待完成")
            return
        
        # 启动处理线程
        self.processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        thread = threading.Thread(target=self.process_videos, daemon=True)
        thread.start()
    
    def stop_processing(self):
        """停止处理"""
        self.processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.log_message("已停止处理")
    
    def process_videos(self):
        """处理视频的后台线程"""
        try:
            api_key = self.api_key_var.get().strip()
            folder_path = self.folder_var.get().strip()
            
            self.log_message("开始处理视频文件...")
            self.log_message("正在初始化字幕生成器...")
            
            # 导入并创建字幕生成器
            from subtitle_generator import SubtitleGenerator
            generator = SubtitleGenerator(api_key)
            
            self.log_message("✓ 字幕生成器初始化成功")
            
            # 处理文件夹
            success_count, total_count = generator.process_folder(
                folder_path, 
                progress_callback=self.log_message
            )
            
            # 完成处理
            if self.processing:  # 检查是否被用户停止
                if success_count > 0:
                    result_msg = f"✓ 处理完成！成功: {success_count}/{total_count}"
                    self.log_message(result_msg)
                    messagebox.showinfo("完成", f"字幕生成完成！\n成功处理: {success_count}/{total_count} 个视频\n\n字幕文件已保存到视频所在文件夹")
                else:
                    result_msg = f"✗ 处理失败！成功: {success_count}/{total_count}"
                    self.log_message(result_msg)
                    messagebox.showwarning("处理失败", 
                                         "没有成功处理任何视频文件\n\n可能的原因：\n1. 网络连接问题\n2. 音频文件过大\n3. API配额不足\n\n请查看日志了解详细信息")
            
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", f"{error_msg}\n\n建议：\n1. 检查网络连接\n2. 验证API密钥\n3. 重启程序重试")
        
        finally:
            # 重置UI状态
            self.processing = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    print(f"启动简化版本 {config.APP_TITLE} v{config.APP_VERSION}")
    
    try:
        # 创建并运行应用程序
        app = SimpleSubtitleGeneratorUI()
        app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"程序运行时发生错误: {str(e)}"
        print(error_msg)
        
        # 尝试显示GUI错误消息
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("运行错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
