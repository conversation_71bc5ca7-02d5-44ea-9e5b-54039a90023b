"""
语音识别辅助模块
集成多种语音识别方案
"""

import os
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, List, Dict
import requests


class SpeechRecognitionHelper:
    """语音识别辅助类"""
    
    def __init__(self):
        self.available_engines = self.check_available_engines()
    
    def check_available_engines(self) -> List[str]:
        """检查可用的语音识别引擎"""
        engines = []
        
        # 检查是否有speech_recognition库
        try:
            import speech_recognition as sr
            engines.append("speech_recognition")
        except ImportError:
            pass
        
        # 检查是否有whisper
        try:
            import whisper
            engines.append("whisper")
        except ImportError:
            pass
        
        # 检查是否有ffmpeg（用于音频转换）
        try:
            subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
            engines.append("ffmpeg")
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        return engines
    
    def transcribe_with_speech_recognition(self, audio_path: str) -> Optional[str]:
        """使用speech_recognition库进行转录"""
        try:
            import speech_recognition as sr
            
            r = sr.Recognizer()
            
            # 转换音频格式为WAV（如果需要）
            wav_path = self.convert_to_wav(audio_path)
            
            with sr.AudioFile(wav_path) as source:
                audio = r.record(source)
            
            # 尝试使用Google语音识别（免费但有限制）
            try:
                text = r.recognize_google(audio, language='zh-CN')
                return self.format_transcription_with_timestamps(text, audio_path)
            except sr.UnknownValueError:
                print("Google语音识别无法理解音频")
                return None
            except sr.RequestError as e:
                print(f"Google语音识别服务错误: {e}")
                return None
                
        except Exception as e:
            print(f"speech_recognition转录失败: {e}")
            return None
    
    def transcribe_with_whisper(self, audio_path: str) -> Optional[str]:
        """使用Whisper进行转录"""
        try:
            import whisper
            
            # 加载Whisper模型（首次使用会下载）
            model = whisper.load_model("base")
            
            # 转录音频
            result = model.transcribe(audio_path, language="zh")
            
            # 格式化结果
            segments = []
            for segment in result["segments"]:
                start_time = self.seconds_to_timestamp(segment["start"])
                end_time = self.seconds_to_timestamp(segment["end"])
                text = segment["text"].strip()
                segments.append(f"[{start_time}-{end_time}] {text}")
            
            return '\n'.join(segments)
            
        except Exception as e:
            print(f"Whisper转录失败: {e}")
            return None
    
    def convert_to_wav(self, audio_path: str) -> str:
        """转换音频文件为WAV格式"""
        try:
            input_path = Path(audio_path)
            output_path = input_path.parent / f"{input_path.stem}_converted.wav"
            
            # 使用ffmpeg转换
            cmd = [
                "ffmpeg", "-i", str(input_path),
                "-acodec", "pcm_s16le",
                "-ar", "16000",
                "-ac", "1",
                "-y",  # 覆盖输出文件
                str(output_path)
            ]
            
            subprocess.run(cmd, capture_output=True, check=True)
            return str(output_path)
            
        except Exception as e:
            print(f"音频转换失败: {e}")
            return audio_path  # 返回原文件
    
    def seconds_to_timestamp(self, seconds: float) -> str:
        """将秒数转换为时间戳格式"""
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"
    
    def format_transcription_with_timestamps(self, text: str, audio_path: str) -> str:
        """为转录文本添加时间戳"""
        try:
            from moviepy.editor import AudioFileClip
            
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            audio.close()
            
            # 简单地将文本分段，每段5秒
            words = text.split()
            if not words:
                return ""
            
            segment_duration = 5  # 每段5秒
            words_per_segment = max(1, len(words) // max(1, int(duration / segment_duration)))
            
            segments = []
            for i in range(0, len(words), words_per_segment):
                start_time = i * segment_duration / words_per_segment
                end_time = min((i + words_per_segment) * segment_duration / words_per_segment, duration)
                
                start_str = self.seconds_to_timestamp(start_time)
                end_str = self.seconds_to_timestamp(end_time)
                
                segment_words = words[i:i + words_per_segment]
                segment_text = ' '.join(segment_words)
                
                segments.append(f"[{start_str}-{end_str}] {segment_text}")
            
            return '\n'.join(segments)
            
        except Exception as e:
            print(f"添加时间戳失败: {e}")
            return f"[00:00-01:00] {text}"
    
    def transcribe_audio(self, audio_path: str) -> Optional[str]:
        """自动选择最佳的语音识别方法"""
        print(f"可用的语音识别引擎: {self.available_engines}")
        
        # 优先使用Whisper（如果可用）
        if "whisper" in self.available_engines:
            print("使用Whisper进行语音识别...")
            result = self.transcribe_with_whisper(audio_path)
            if result:
                return result
        
        # 备选：使用speech_recognition
        if "speech_recognition" in self.available_engines:
            print("使用speech_recognition进行语音识别...")
            result = self.transcribe_with_speech_recognition(audio_path)
            if result:
                return result
        
        # 如果都不可用，返回模拟结果
        print("警告: 没有可用的语音识别引擎，生成模拟转录")
        return self.generate_mock_transcription(audio_path)
    
    def generate_mock_transcription(self, audio_path: str) -> str:
        """生成模拟的转录文本"""
        try:
            from moviepy.editor import AudioFileClip
            
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            audio.close()
            
            # 生成基于时长的模拟转录
            segments = []
            segment_duration = 5
            segment_count = int(duration / segment_duration) + 1
            
            for i in range(segment_count):
                start = i * segment_duration
                end = min((i + 1) * segment_duration, duration)
                
                if start >= duration:
                    break
                
                start_str = self.seconds_to_timestamp(start)
                end_str = self.seconds_to_timestamp(end)
                
                segments.append(f"[{start_str}-{end_str}] 这里是第{i+1}段音频内容的转录文本")
            
            return '\n'.join(segments)
            
        except Exception as e:
            print(f"生成模拟转录失败: {e}")
            return "[00:00-00:05] 音频转录文本"


def install_speech_recognition_dependencies():
    """安装语音识别依赖"""
    print("正在安装语音识别依赖...")
    
    dependencies = [
        "SpeechRecognition",
        "pyaudio",
        "openai-whisper"
    ]
    
    for dep in dependencies:
        try:
            subprocess.run([
                "pip", "install", dep
            ], check=True)
            print(f"✓ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {dep} 安装失败")


def main():
    """测试语音识别功能"""
    helper = SpeechRecognitionHelper()
    print(f"可用的语音识别引擎: {helper.available_engines}")
    
    if not helper.available_engines:
        print("没有可用的语音识别引擎")
        print("建议安装以下依赖：")
        print("pip install SpeechRecognition pyaudio")
        print("pip install openai-whisper")


if __name__ == "__main__":
    main()
