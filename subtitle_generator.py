"""
字幕生成器核心模块
"""
import os
import tempfile
import time
import socket
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import google.generativeai as genai
from moviepy.editor import VideoFileClip
import config
from network_helper import NetworkHelper


class SubtitleGenerator:
    def __init__(self, api_key: str):
        """初始化字幕生成器"""
        self.api_key = api_key
        self.network_helper = NetworkHelper()
        self.setup_gemini_api()
        
    def check_network_connection(self) -> bool:
        """检查网络连接"""
        try:
            # 尝试连接Google的DNS服务器
            socket.create_connection(("*******", 53), timeout=5)
            return True
        except OSError:
            return False

    def setup_gemini_api(self):
        """设置Gemini API"""
        if not self.api_key:
            raise ValueError("Gemini API密钥不能为空")

        # 检查网络连接
        if not self.check_network_connection():
            raise ConnectionError("网络连接失败，请检查网络设置")

        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(config.GEMINI_MODEL)
        print("Gemini API初始化成功")
    
    def extract_audio_from_video(self, video_path: str, output_path: str) -> bool:
        """从视频中提取音频"""
        try:
            if not os.path.exists(video_path):
                print(f"错误: 视频文件不存在: {video_path}")
                return False

            video = VideoFileClip(video_path)
            audio = video.audio

            if audio is None:
                print(f"警告: 视频 {video_path} 没有音轨")
                video.close()
                return False

            # 检查音频时长
            audio_duration = audio.duration
            print(f"音频时长: {audio_duration:.1f}秒")

            # 导出音频，根据配置决定是否压缩
            if config.AUDIO_COMPRESSION_ENABLED:
                # 压缩音频以减小文件大小
                audio.write_audiofile(
                    output_path,
                    verbose=False,
                    logger=None,
                    fps=config.AUDIO_SAMPLE_RATE,  # 降低采样率
                    bitrate=config.AUDIO_BITRATE,  # 降低比特率
                    codec='mp3'  # 使用MP3编码进一步压缩
                )
                print(f"音频已压缩: 采样率={config.AUDIO_SAMPLE_RATE}Hz, 比特率={config.AUDIO_BITRATE}, 编码=MP3")
            else:
                # 原始质量导出
                audio.write_audiofile(output_path, verbose=False, logger=None)

            # 清理资源
            audio.close()
            video.close()

            # 验证音频文件是否成功创建
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                print(f"音频提取成功: {output_path}")
                return True
            else:
                print(f"音频文件创建失败: {output_path}")
                return False

        except Exception as e:
            print(f"提取音频失败: {str(e)}")
            print(f"视频路径: {video_path}")
            print(f"输出路径: {output_path}")
            return False
    
    def transcribe_audio(self, audio_path: str) -> Optional[str]:
        """使用Gemini API转录音频"""
        try:
            # 检查文件大小
            file_size_mb = os.path.getsize(audio_path) / (1024 * 1024)
            if file_size_mb > config.MAX_FILE_SIZE_MB:
                print(f"音频文件过大: {file_size_mb:.1f}MB，超过限制 {config.MAX_FILE_SIZE_MB}MB")
                return None

            print(f"开始上传音频文件: {file_size_mb:.1f}MB")

            # 使用网络辅助工具上传文件
            audio_file = self.network_helper.upload_with_retry(audio_path, max_retries=5)

            if not audio_file:
                print("音频文件上传失败")
                return None

            # 等待文件处理完成，增加超时控制
            max_wait_time = 300  # 5分钟超时
            wait_time = 0

            while audio_file.state.name == "PROCESSING" and wait_time < max_wait_time:
                time.sleep(2)
                wait_time += 2
                try:
                    audio_file = genai.get_file(audio_file.name)
                    print(f"文件处理中... ({wait_time}s)")
                except Exception as e:
                    print(f"检查文件状态失败: {str(e)}")
                    break

            if wait_time >= max_wait_time:
                print("文件处理超时")
                try:
                    genai.delete_file(audio_file.name)
                except:
                    pass
                return None

            if audio_file.state.name == "FAILED":
                print("音频文件处理失败")
                try:
                    genai.delete_file(audio_file.name)
                except:
                    pass
                return None

            print("开始生成转录...")

            # 生成转录，使用网络辅助工具
            prompt = """
            请将这个音频文件转录为文本，并提供时间戳信息。
            请按照以下格式返回：
            [开始时间-结束时间] 转录文本

            例如：
            [00:00-00:05] 欢迎观看这个视频
            [00:05-00:10] 今天我们要讲解的内容是

            请确保时间戳准确，文本清晰。
            """

            response = self.network_helper.generate_content_with_retry(
                self.model, [prompt, audio_file], max_retries=3
            )

            # 清理上传的文件
            try:
                genai.delete_file(audio_file.name)
                print("临时文件清理完成")
            except Exception as e:
                print(f"清理临时文件失败: {str(e)}")

            if response and response.text:
                print("转录完成")
                return response.text
            else:
                print("转录结果为空")
                return None

        except Exception as e:
            print(f"音频转录失败: {str(e)}")
            return None
    
    def translate_text(self, text: str, target_language: str = "English") -> Optional[str]:
        """翻译文本"""
        try:
            print(f"开始翻译文本为{target_language}...")

            prompt = f"""
            请将以下文本翻译为{target_language}，保持原有的时间戳格式：

            {text}

            请确保：
            1. 保持时间戳格式不变
            2. 翻译准确自然
            3. 保持原文的分段结构
            """

            # 使用网络辅助工具进行翻译
            response = self.network_helper.generate_content_with_retry(
                self.model, prompt, max_retries=3
            )

            if response and response.text:
                return response.text
            else:
                print("翻译失败")
                return None

        except Exception as e:
            print(f"文本翻译失败: {str(e)}")
            return None
    
    def parse_timestamped_text(self, text: str) -> List[Dict[str, str]]:
        """解析带时间戳的文本"""
        lines = text.strip().split('\n')
        segments = []
        
        for line in lines:
            line = line.strip()
            if not line or not line.startswith('['):
                continue
                
            try:
                # 解析格式: [00:00-00:05] 文本内容
                timestamp_end = line.find(']')
                if timestamp_end == -1:
                    continue
                    
                timestamp_part = line[1:timestamp_end]
                text_part = line[timestamp_end + 1:].strip()
                
                if '-' not in timestamp_part:
                    continue
                    
                start_time, end_time = timestamp_part.split('-')
                
                segments.append({
                    'start': start_time.strip(),
                    'end': end_time.strip(),
                    'text': text_part
                })
                
            except Exception as e:
                print(f"解析时间戳失败: {line}, 错误: {str(e)}")
                continue
        
        return segments
    
    def time_to_srt_format(self, time_str: str) -> str:
        """将时间格式转换为SRT格式"""
        try:
            # 输入格式: 00:00 或 00:00:00
            # 输出格式: 00:00:00,000
            
            parts = time_str.split(':')
            if len(parts) == 2:
                # MM:SS格式
                minutes, seconds = parts
                return f"00:{minutes:0>2}:{seconds:0>2},000"
            elif len(parts) == 3:
                # HH:MM:SS格式
                hours, minutes, seconds = parts
                return f"{hours:0>2}:{minutes:0>2}:{seconds:0>2},000"
            else:
                return "00:00:00,000"
                
        except Exception:
            return "00:00:00,000"
    
    def generate_srt_content(self, chinese_segments: List[Dict], english_segments: List[Dict]) -> str:
        """生成SRT字幕内容"""
        srt_content = []
        
        # 确保两个列表长度相同
        max_len = max(len(chinese_segments), len(english_segments))
        
        for i in range(max_len):
            chinese_text = chinese_segments[i]['text'] if i < len(chinese_segments) else ""
            english_text = english_segments[i]['text'] if i < len(english_segments) else ""
            
            # 使用中文时间戳，如果没有则使用英文时间戳
            if i < len(chinese_segments):
                start_time = self.time_to_srt_format(chinese_segments[i]['start'])
                end_time = self.time_to_srt_format(chinese_segments[i]['end'])
            elif i < len(english_segments):
                start_time = self.time_to_srt_format(english_segments[i]['start'])
                end_time = self.time_to_srt_format(english_segments[i]['end'])
            else:
                continue
            
            # SRT格式
            srt_content.append(f"{i + 1}")
            srt_content.append(f"{start_time} --> {end_time}")
            
            # 双语字幕：中文在上，英文在下
            if chinese_text and english_text:
                srt_content.append(f"{chinese_text}")
                srt_content.append(f"{english_text}")
            elif chinese_text:
                srt_content.append(f"{chinese_text}")
            elif english_text:
                srt_content.append(f"{english_text}")
            
            srt_content.append("")  # 空行分隔
        
        return '\n'.join(srt_content)
    
    def process_video(self, video_path: str, progress_callback=None) -> bool:
        """处理单个视频文件"""
        try:
            video_name = Path(video_path).stem
            video_dir = Path(video_path).parent
            srt_path = video_dir / f"{video_name}.srt"
            
            if progress_callback:
                progress_callback(f"开始处理: {video_name}")
            
            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
                temp_audio_path = temp_audio.name
            
            try:
                # 1. 提取音频
                if progress_callback:
                    progress_callback(f"提取音频: {video_name}")
                
                if not self.extract_audio_from_video(video_path, temp_audio_path):
                    return False
                
                # 2. 转录音频（中文）
                if progress_callback:
                    progress_callback(f"转录音频: {video_name}")
                
                chinese_text = self.transcribe_audio(temp_audio_path)
                if not chinese_text:
                    return False
                
                # 3. 翻译为英文
                if progress_callback:
                    progress_callback(f"翻译文本: {video_name}")
                
                english_text = self.translate_text(chinese_text, "English")
                if not english_text:
                    return False
                
                # 4. 解析时间戳
                chinese_segments = self.parse_timestamped_text(chinese_text)
                english_segments = self.parse_timestamped_text(english_text)
                
                if not chinese_segments and not english_segments:
                    print("未能解析到有效的时间戳信息")
                    return False
                
                # 5. 生成SRT内容
                srt_content = self.generate_srt_content(chinese_segments, english_segments)
                
                # 6. 保存SRT文件
                try:
                    # 确保目录存在
                    srt_path.parent.mkdir(parents=True, exist_ok=True)

                    with open(srt_path, 'w', encoding=config.SRT_ENCODING) as f:
                        f.write(srt_content)

                    # 验证文件是否成功创建
                    if srt_path.exists() and srt_path.stat().st_size > 0:
                        if progress_callback:
                            progress_callback(f"✓ 字幕已保存: {srt_path}")
                        return True
                    else:
                        if progress_callback:
                            progress_callback(f"✗ 字幕文件保存失败: {srt_path}")
                        return False

                except Exception as save_error:
                    if progress_callback:
                        progress_callback(f"✗ 保存字幕文件时出错: {str(save_error)}")
                    return False
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_audio_path):
                    os.unlink(temp_audio_path)
                    
        except Exception as e:
            if progress_callback:
                progress_callback(f"✗ 处理失败: {video_name}")
                progress_callback(f"  错误详情: {str(e)}")
                progress_callback(f"  视频路径: {video_path}")
                progress_callback(f"  目标字幕路径: {srt_path}")
            print(f"处理视频时发生异常: {str(e)}")
            return False
    
    def process_folder(self, folder_path: str, progress_callback=None) -> Tuple[int, int]:
        """处理文件夹中的所有视频"""
        folder = Path(folder_path)
        video_files = []
        
        # 查找所有视频文件
        for ext in config.SUPPORTED_VIDEO_FORMATS:
            # 搜索小写扩展名
            video_files.extend(folder.glob(f"*{ext}"))
            # 搜索大写扩展名（避免重复）
            upper_ext = ext.upper()
            if upper_ext != ext:
                video_files.extend(folder.glob(f"*{upper_ext}"))

        # 去除重复文件（以防万一）
        video_files = list(set(video_files))
        
        if not video_files:
            if progress_callback:
                progress_callback("未找到支持的视频文件")
            return 0, 0
        
        success_count = 0
        total_count = len(video_files)
        
        if progress_callback:
            progress_callback(f"找到 {total_count} 个视频文件")
        
        for i, video_file in enumerate(video_files, 1):
            if progress_callback:
                progress_callback(f"处理进度: {i}/{total_count}")
            
            if self.process_video(str(video_file), progress_callback):
                success_count += 1
        
        return success_count, total_count
