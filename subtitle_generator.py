"""
字幕生成器核心模块
"""
import os
import tempfile
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import google.generativeai as genai
from moviepy.editor import VideoFileClip
import config


class SubtitleGenerator:
    def __init__(self, api_key: str):
        """初始化字幕生成器"""
        self.api_key = api_key
        self.setup_gemini_api()
        
    def setup_gemini_api(self):
        """设置Gemini API"""
        if not self.api_key:
            raise ValueError("Gemini API密钥不能为空")
        
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(config.GEMINI_MODEL)
    
    def extract_audio_from_video(self, video_path: str, output_path: str) -> bool:
        """从视频中提取音频"""
        try:
            if not os.path.exists(video_path):
                print(f"错误: 视频文件不存在: {video_path}")
                return False

            video = VideoFileClip(video_path)
            audio = video.audio

            if audio is None:
                print(f"警告: 视频 {video_path} 没有音轨")
                video.close()
                return False

            # 导出音频为WAV格式
            audio.write_audiofile(output_path, verbose=False, logger=None)

            # 清理资源
            audio.close()
            video.close()

            # 验证音频文件是否成功创建
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                print(f"音频提取成功: {output_path}")
                return True
            else:
                print(f"音频文件创建失败: {output_path}")
                return False

        except Exception as e:
            print(f"提取音频失败: {str(e)}")
            print(f"视频路径: {video_path}")
            print(f"输出路径: {output_path}")
            return False
    
    def transcribe_audio(self, audio_path: str) -> Optional[str]:
        """使用Gemini API转录音频"""
        try:
            # 检查文件大小
            file_size_mb = os.path.getsize(audio_path) / (1024 * 1024)
            if file_size_mb > config.MAX_FILE_SIZE_MB:
                print(f"音频文件过大: {file_size_mb:.1f}MB，超过限制 {config.MAX_FILE_SIZE_MB}MB")
                return None
            
            # 上传音频文件
            audio_file = genai.upload_file(audio_path)
            
            # 等待文件处理完成
            while audio_file.state.name == "PROCESSING":
                time.sleep(1)
                audio_file = genai.get_file(audio_file.name)
            
            if audio_file.state.name == "FAILED":
                print("音频文件处理失败")
                return None
            
            # 生成转录
            prompt = """
            请将这个音频文件转录为文本，并提供时间戳信息。
            请按照以下格式返回：
            [开始时间-结束时间] 转录文本
            
            例如：
            [00:00-00:05] 欢迎观看这个视频
            [00:05-00:10] 今天我们要讲解的内容是
            
            请确保时间戳准确，文本清晰。
            """
            
            response = self.model.generate_content([prompt, audio_file])
            
            # 清理上传的文件
            genai.delete_file(audio_file.name)
            
            return response.text
            
        except Exception as e:
            print(f"音频转录失败: {str(e)}")
            return None
    
    def translate_text(self, text: str, target_language: str = "English") -> Optional[str]:
        """翻译文本"""
        try:
            prompt = f"""
            请将以下文本翻译为{target_language}，保持原有的时间戳格式：
            
            {text}
            
            请确保：
            1. 保持时间戳格式不变
            2. 翻译准确自然
            3. 保持原文的分段结构
            """
            
            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            print(f"文本翻译失败: {str(e)}")
            return None
    
    def parse_timestamped_text(self, text: str) -> List[Dict[str, str]]:
        """解析带时间戳的文本"""
        lines = text.strip().split('\n')
        segments = []
        
        for line in lines:
            line = line.strip()
            if not line or not line.startswith('['):
                continue
                
            try:
                # 解析格式: [00:00-00:05] 文本内容
                timestamp_end = line.find(']')
                if timestamp_end == -1:
                    continue
                    
                timestamp_part = line[1:timestamp_end]
                text_part = line[timestamp_end + 1:].strip()
                
                if '-' not in timestamp_part:
                    continue
                    
                start_time, end_time = timestamp_part.split('-')
                
                segments.append({
                    'start': start_time.strip(),
                    'end': end_time.strip(),
                    'text': text_part
                })
                
            except Exception as e:
                print(f"解析时间戳失败: {line}, 错误: {str(e)}")
                continue
        
        return segments
    
    def time_to_srt_format(self, time_str: str) -> str:
        """将时间格式转换为SRT格式"""
        try:
            # 输入格式: 00:00 或 00:00:00
            # 输出格式: 00:00:00,000
            
            parts = time_str.split(':')
            if len(parts) == 2:
                # MM:SS格式
                minutes, seconds = parts
                return f"00:{minutes:0>2}:{seconds:0>2},000"
            elif len(parts) == 3:
                # HH:MM:SS格式
                hours, minutes, seconds = parts
                return f"{hours:0>2}:{minutes:0>2}:{seconds:0>2},000"
            else:
                return "00:00:00,000"
                
        except Exception:
            return "00:00:00,000"
    
    def generate_srt_content(self, chinese_segments: List[Dict], english_segments: List[Dict]) -> str:
        """生成SRT字幕内容"""
        srt_content = []
        
        # 确保两个列表长度相同
        max_len = max(len(chinese_segments), len(english_segments))
        
        for i in range(max_len):
            chinese_text = chinese_segments[i]['text'] if i < len(chinese_segments) else ""
            english_text = english_segments[i]['text'] if i < len(english_segments) else ""
            
            # 使用中文时间戳，如果没有则使用英文时间戳
            if i < len(chinese_segments):
                start_time = self.time_to_srt_format(chinese_segments[i]['start'])
                end_time = self.time_to_srt_format(chinese_segments[i]['end'])
            elif i < len(english_segments):
                start_time = self.time_to_srt_format(english_segments[i]['start'])
                end_time = self.time_to_srt_format(english_segments[i]['end'])
            else:
                continue
            
            # SRT格式
            srt_content.append(f"{i + 1}")
            srt_content.append(f"{start_time} --> {end_time}")
            
            # 双语字幕：中文在上，英文在下
            if chinese_text and english_text:
                srt_content.append(f"{chinese_text}")
                srt_content.append(f"{english_text}")
            elif chinese_text:
                srt_content.append(f"{chinese_text}")
            elif english_text:
                srt_content.append(f"{english_text}")
            
            srt_content.append("")  # 空行分隔
        
        return '\n'.join(srt_content)
    
    def process_video(self, video_path: str, progress_callback=None) -> bool:
        """处理单个视频文件"""
        try:
            video_name = Path(video_path).stem
            video_dir = Path(video_path).parent
            srt_path = video_dir / f"{video_name}.srt"
            
            if progress_callback:
                progress_callback(f"开始处理: {video_name}")
            
            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
                temp_audio_path = temp_audio.name
            
            try:
                # 1. 提取音频
                if progress_callback:
                    progress_callback(f"提取音频: {video_name}")
                
                if not self.extract_audio_from_video(video_path, temp_audio_path):
                    return False
                
                # 2. 转录音频（中文）
                if progress_callback:
                    progress_callback(f"转录音频: {video_name}")
                
                chinese_text = self.transcribe_audio(temp_audio_path)
                if not chinese_text:
                    return False
                
                # 3. 翻译为英文
                if progress_callback:
                    progress_callback(f"翻译文本: {video_name}")
                
                english_text = self.translate_text(chinese_text, "English")
                if not english_text:
                    return False
                
                # 4. 解析时间戳
                chinese_segments = self.parse_timestamped_text(chinese_text)
                english_segments = self.parse_timestamped_text(english_text)
                
                if not chinese_segments and not english_segments:
                    print("未能解析到有效的时间戳信息")
                    return False
                
                # 5. 生成SRT内容
                srt_content = self.generate_srt_content(chinese_segments, english_segments)
                
                # 6. 保存SRT文件
                try:
                    # 确保目录存在
                    srt_path.parent.mkdir(parents=True, exist_ok=True)

                    with open(srt_path, 'w', encoding=config.SRT_ENCODING) as f:
                        f.write(srt_content)

                    # 验证文件是否成功创建
                    if srt_path.exists() and srt_path.stat().st_size > 0:
                        if progress_callback:
                            progress_callback(f"✓ 字幕已保存: {srt_path}")
                        return True
                    else:
                        if progress_callback:
                            progress_callback(f"✗ 字幕文件保存失败: {srt_path}")
                        return False

                except Exception as save_error:
                    if progress_callback:
                        progress_callback(f"✗ 保存字幕文件时出错: {str(save_error)}")
                    return False
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_audio_path):
                    os.unlink(temp_audio_path)
                    
        except Exception as e:
            if progress_callback:
                progress_callback(f"✗ 处理失败: {video_name}")
                progress_callback(f"  错误详情: {str(e)}")
                progress_callback(f"  视频路径: {video_path}")
                progress_callback(f"  目标字幕路径: {srt_path}")
            print(f"处理视频时发生异常: {str(e)}")
            return False
    
    def process_folder(self, folder_path: str, progress_callback=None) -> Tuple[int, int]:
        """处理文件夹中的所有视频"""
        folder = Path(folder_path)
        video_files = []
        
        # 查找所有视频文件
        for ext in config.SUPPORTED_VIDEO_FORMATS:
            # 搜索小写扩展名
            video_files.extend(folder.glob(f"*{ext}"))
            # 搜索大写扩展名（避免重复）
            upper_ext = ext.upper()
            if upper_ext != ext:
                video_files.extend(folder.glob(f"*{upper_ext}"))

        # 去除重复文件（以防万一）
        video_files = list(set(video_files))
        
        if not video_files:
            if progress_callback:
                progress_callback("未找到支持的视频文件")
            return 0, 0
        
        success_count = 0
        total_count = len(video_files)
        
        if progress_callback:
            progress_callback(f"找到 {total_count} 个视频文件")
        
        for i, video_file in enumerate(video_files, 1):
            if progress_callback:
                progress_callback(f"处理进度: {i}/{total_count}")
            
            if self.process_video(str(video_file), progress_callback):
                success_count += 1
        
        return success_count, total_count
