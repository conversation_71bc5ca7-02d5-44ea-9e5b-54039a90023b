#!/usr/bin/env python3
"""
DeepSeek版本测试脚本
验证API连接和功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import config
    from deepseek_subtitle_generator import DeepSeekSubtitleGenerator
    from speech_recognition_helper import SpeechRecognitionHelper
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


def test_api_connection(api_key: str):
    """测试DeepSeek API连接"""
    print("测试DeepSeek API连接...")
    
    try:
        generator = DeepSeekSubtitleGenerator(api_key)
        print("✓ DeepSeek API连接成功")
        return True
    except Exception as e:
        print(f"✗ DeepSeek API连接失败: {str(e)}")
        return False


def test_speech_recognition():
    """测试语音识别功能"""
    print("\n测试语音识别功能...")
    
    helper = SpeechRecognitionHelper()
    print(f"可用的语音识别引擎: {helper.available_engines}")
    
    if helper.available_engines:
        print("✓ 语音识别功能可用")
        return True
    else:
        print("⚠️  没有可用的语音识别引擎，将使用模拟转录")
        return False


def test_text_translation(api_key: str):
    """测试文本翻译功能"""
    print("\n测试文本翻译功能...")
    
    try:
        generator = DeepSeekSubtitleGenerator(api_key)
        
        test_text = "[00:00-00:05] 这是一个测试文本"
        result = generator.translate_text_with_deepseek(test_text, "English")
        
        if result:
            print("✓ 文本翻译功能正常")
            print(f"翻译结果: {result[:100]}...")
            return True
        else:
            print("✗ 文本翻译失败")
            return False
            
    except Exception as e:
        print(f"✗ 文本翻译测试失败: {str(e)}")
        return False


def test_srt_generation():
    """测试SRT文件生成"""
    print("\n测试SRT文件生成...")
    
    try:
        # 创建测试数据
        chinese_segments = [
            {'start': '00:00', 'end': '00:05', 'text': '欢迎使用DeepSeek字幕生成器'},
            {'start': '00:05', 'end': '00:10', 'text': '这是一个测试字幕'},
        ]
        
        english_segments = [
            {'start': '00:00', 'end': '00:05', 'text': 'Welcome to DeepSeek Subtitle Generator'},
            {'start': '00:05', 'end': '00:10', 'text': 'This is a test subtitle'},
        ]
        
        # 使用虚拟API密钥创建生成器（仅用于测试SRT生成）
        generator = DeepSeekSubtitleGenerator("test-key")
        srt_content = generator.generate_srt_content(chinese_segments, english_segments)
        
        if srt_content and "欢迎使用DeepSeek字幕生成器" in srt_content:
            print("✓ SRT文件生成功能正常")
            print("生成的SRT内容预览:")
            print("-" * 30)
            print(srt_content[:200] + "...")
            print("-" * 30)
            return True
        else:
            print("✗ SRT文件生成失败")
            return False
            
    except Exception as e:
        print(f"✗ SRT生成测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("=" * 50)
    print("DeepSeek视频字幕生成器 - 功能测试")
    print("=" * 50)
    
    # 获取API密钥
    api_key = input("请输入DeepSeek API密钥（测试API连接，直接回车跳过）: ").strip()
    
    test_results = []
    
    # 测试SRT生成（不需要API密钥）
    test_results.append(("SRT文件生成", test_srt_generation()))
    
    # 测试语音识别
    test_results.append(("语音识别", test_speech_recognition()))
    
    if api_key:
        # 测试API连接
        test_results.append(("API连接", test_api_connection(api_key)))
        
        # 测试文本翻译
        test_results.append(("文本翻译", test_text_translation(api_key)))
    else:
        print("\n跳过API相关测试（未提供API密钥）")
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！DeepSeek版本可以正常使用")
    elif passed > 0:
        print("\n⚠️  部分测试通过，程序可以运行但可能功能受限")
    else:
        print("\n❌ 所有测试失败，请检查安装和配置")
    
    print("\n使用建议：")
    if api_key:
        print("1. 运行 python deepseek_main.py 启动程序")
    else:
        print("1. 获取有效的DeepSeek API密钥")
        print("2. 运行 python deepseek_main.py 启动程序")
    
    print("3. 如果语音识别功能不可用，程序会使用模拟转录")
    print("4. 建议安装Whisper以获得更好的语音识别效果：pip install openai-whisper")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {e}")
    
    input("\n按回车键退出...")
