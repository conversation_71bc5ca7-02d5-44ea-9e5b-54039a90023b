#!/usr/bin/env python3
"""
安装测试脚本
用于验证所有依赖是否正确安装
"""

import sys
import os

def test_imports():
    """测试所有必要的模块导入"""
    print("测试模块导入...")
    
    tests = [
        ("tkinter", "GUI界面库"),
        ("google.generativeai", "Google Gemini API"),
        ("moviepy.editor", "视频处理库"),
        ("pathlib", "路径处理库"),
        ("threading", "多线程库"),
        ("tempfile", "临时文件库"),
    ]
    
    failed = []
    
    for module, description in tests:
        try:
            __import__(module)
            print(f"✓ {module} - {description}")
        except ImportError as e:
            print(f"✗ {module} - {description} (错误: {e})")
            failed.append(module)
    
    return failed

def test_config():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    try:
        import config
        print(f"✓ 配置文件加载成功")
        print(f"  - 应用标题: {config.APP_TITLE}")
        print(f"  - 版本: {config.APP_VERSION}")
        print(f"  - 支持的视频格式: {len(config.SUPPORTED_VIDEO_FORMATS)} 种")
        return True
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False

def test_modules():
    """测试自定义模块"""
    print("\n测试自定义模块...")
    
    modules = [
        ("subtitle_generator", "字幕生成器"),
        ("ui", "用户界面"),
    ]
    
    failed = []
    
    for module, description in modules:
        try:
            __import__(module)
            print(f"✓ {module} - {description}")
        except Exception as e:
            print(f"✗ {module} - {description} (错误: {e})")
            failed.append(module)
    
    return failed

def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = [
        ("main.py", "主程序文件"),
        ("config.py", "配置文件"),
        ("subtitle_generator.py", "字幕生成器"),
        ("ui.py", "用户界面"),
        ("requirements.txt", "依赖列表"),
        ("README.md", "项目说明"),
        ("USAGE.md", "使用说明"),
    ]
    
    missing = []
    
    for filename, description in required_files:
        if os.path.exists(filename):
            print(f"✓ {filename} - {description}")
        else:
            print(f"✗ {filename} - {description} (文件不存在)")
            missing.append(filename)
    
    return missing

def main():
    """主测试函数"""
    print("=" * 50)
    print("视频字幕自动生成器 - 安装测试")
    print("=" * 50)
    
    # 测试Python版本
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 7):
        print("⚠️  警告: 建议使用Python 3.7或更高版本")
    
    # 运行各项测试
    failed_imports = test_imports()
    config_ok = test_config()
    failed_modules = test_modules()
    missing_files = test_file_structure()
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    all_good = True
    
    if failed_imports:
        print(f"✗ 缺少依赖模块: {', '.join(failed_imports)}")
        print("  解决方案: 运行 'pip install -r requirements.txt'")
        all_good = False
    
    if not config_ok:
        print("✗ 配置文件有问题")
        all_good = False
    
    if failed_modules:
        print(f"✗ 自定义模块有问题: {', '.join(failed_modules)}")
        all_good = False
    
    if missing_files:
        print(f"✗ 缺少文件: {', '.join(missing_files)}")
        all_good = False
    
    if all_good:
        print("✓ 所有测试通过！程序可以正常运行")
        print("\n启动程序: python main.py")
        return 0
    else:
        print("✗ 发现问题，请根据上述提示解决")
        return 1

if __name__ == "__main__":
    sys.exit(main())
