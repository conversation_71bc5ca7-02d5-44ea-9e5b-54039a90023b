#!/usr/bin/env python3
"""
模拟字幕生成测试
不需要真实的API调用，用于测试文件保存逻辑
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import config
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class MockSubtitleGenerator:
    """模拟字幕生成器，用于测试文件保存逻辑"""
    
    def __init__(self):
        pass
    
    def generate_mock_srt_content(self):
        """生成模拟的SRT内容"""
        return """1
00:00:00,000 --> 00:00:05,000
这是第一段中文字幕
This is the first English subtitle

2
00:00:05,000 --> 00:00:10,000
这是第二段中文字幕
This is the second English subtitle

3
00:00:10,000 --> 00:00:15,000
这是第三段中文字幕
This is the third English subtitle
"""
    
    def process_video_mock(self, video_path: str, progress_callback=None) -> bool:
        """模拟处理单个视频文件"""
        try:
            video_name = Path(video_path).stem
            video_dir = Path(video_path).parent
            srt_path = video_dir / f"{video_name}.srt"
            
            if progress_callback:
                progress_callback(f"开始处理: {video_name}")
            
            # 模拟处理步骤
            if progress_callback:
                progress_callback(f"提取音频: {video_name}")
            
            if progress_callback:
                progress_callback(f"转录音频: {video_name}")
            
            if progress_callback:
                progress_callback(f"翻译文本: {video_name}")
            
            # 生成模拟SRT内容
            srt_content = self.generate_mock_srt_content()
            
            # 保存SRT文件
            try:
                # 确保目录存在
                srt_path.parent.mkdir(parents=True, exist_ok=True)
                
                if progress_callback:
                    progress_callback(f"保存字幕到: {srt_path}")
                
                with open(srt_path, 'w', encoding=config.SRT_ENCODING) as f:
                    f.write(srt_content)
                
                # 验证文件是否成功创建
                if srt_path.exists() and srt_path.stat().st_size > 0:
                    if progress_callback:
                        progress_callback(f"✓ 字幕已保存: {srt_path}")
                        progress_callback(f"  文件大小: {srt_path.stat().st_size} 字节")
                    return True
                else:
                    if progress_callback:
                        progress_callback(f"✗ 字幕文件保存失败: {srt_path}")
                    return False
                    
            except Exception as save_error:
                if progress_callback:
                    progress_callback(f"✗ 保存字幕文件时出错: {str(save_error)}")
                return False
                
        except Exception as e:
            if progress_callback:
                progress_callback(f"✗ 处理失败: {video_name}")
                progress_callback(f"  错误详情: {str(e)}")
            return False
    
    def process_folder_mock(self, folder_path: str, progress_callback=None):
        """模拟处理文件夹中的所有视频"""
        folder = Path(folder_path)
        video_files = []
        
        # 查找所有视频文件
        for ext in config.SUPPORTED_VIDEO_FORMATS:
            video_files.extend(folder.glob(f"*{ext}"))
            video_files.extend(folder.glob(f"*{ext.upper()}"))
        
        if not video_files:
            if progress_callback:
                progress_callback("未找到支持的视频文件")
            return 0, 0
        
        success_count = 0
        total_count = len(video_files)
        
        if progress_callback:
            progress_callback(f"找到 {total_count} 个视频文件")
            for i, video_file in enumerate(video_files):
                progress_callback(f"  {i+1}. {video_file.name}")
        
        for i, video_file in enumerate(video_files, 1):
            if progress_callback:
                progress_callback(f"处理进度: {i}/{total_count}")
            
            if self.process_video_mock(str(video_file), progress_callback):
                success_count += 1
        
        return success_count, total_count


def create_test_videos(test_dir: Path):
    """创建测试视频文件（空文件，仅用于测试）"""
    test_videos = [
        "test_video1.mp4",
        "test_video2.avi",
        "test_video3.mov"
    ]
    
    created_files = []
    
    for video_name in test_videos:
        video_path = test_dir / video_name
        try:
            # 创建空的测试文件
            with open(video_path, 'w') as f:
                f.write("# 这是一个测试视频文件")
            created_files.append(video_path)
            print(f"创建测试文件: {video_path}")
        except Exception as e:
            print(f"创建测试文件失败: {video_path} - {str(e)}")
    
    return created_files


def main():
    """主测试函数"""
    print("=" * 60)
    print("模拟字幕生成测试")
    print("=" * 60)
    
    # 创建测试目录
    test_dir = Path("test_videos")
    test_dir.mkdir(exist_ok=True)
    
    try:
        # 创建测试视频文件
        print("创建测试视频文件...")
        test_videos = create_test_videos(test_dir)
        
        if not test_videos:
            print("无法创建测试文件，退出测试")
            return
        
        print(f"创建了 {len(test_videos)} 个测试文件")
        print()
        
        # 创建模拟字幕生成器
        generator = MockSubtitleGenerator()
        
        # 定义进度回调函数
        def progress_callback(message):
            print(f"[进度] {message}")
        
        # 测试处理文件夹
        print("开始模拟处理...")
        print("-" * 40)
        
        success_count, total_count = generator.process_folder_mock(
            str(test_dir), 
            progress_callback=progress_callback
        )
        
        print("-" * 40)
        print(f"处理完成！成功: {success_count}/{total_count}")
        
        # 验证生成的字幕文件
        print("\n验证生成的字幕文件:")
        srt_files = list(test_dir.glob("*.srt"))
        
        for srt_file in srt_files:
            print(f"✓ 找到字幕文件: {srt_file}")
            print(f"  文件大小: {srt_file.stat().st_size} 字节")
            
            # 显示文件内容的前几行
            try:
                with open(srt_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:6]  # 显示前6行
                print("  内容预览:")
                for line in lines:
                    print(f"    {line.rstrip()}")
                print("    ...")
            except Exception as e:
                print(f"  读取文件失败: {str(e)}")
            print()
        
        if len(srt_files) == len(test_videos):
            print("✅ 所有视频都成功生成了字幕文件！")
        else:
            print(f"⚠️  只有 {len(srt_files)}/{len(test_videos)} 个视频生成了字幕文件")
    
    finally:
        # 清理测试文件
        print("\n清理测试文件...")
        try:
            import shutil
            shutil.rmtree(test_dir)
            print("✓ 测试文件清理完成")
        except Exception as e:
            print(f"清理测试文件失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    print("\n如果这个测试成功，说明字幕文件保存逻辑是正常的。")
    print("如果实际使用中遇到问题，可能的原因包括：")
    print("1. API调用失败（网络问题、密钥问题等）")
    print("2. 音频提取失败（视频文件损坏、格式不支持等）")
    print("3. 权限问题（无法写入目标文件夹）")
    print("4. 磁盘空间不足")
    print("\n建议使用调试版本程序 (python debug_main.py) 来获取详细日志。")


if __name__ == "__main__":
    main()
