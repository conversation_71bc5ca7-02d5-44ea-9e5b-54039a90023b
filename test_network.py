#!/usr/bin/env python3
"""
网络连接测试工具
用于诊断网络连接问题
"""

import socket
import time
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import google.generativeai as genai
    import config
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


def test_basic_network():
    """测试基本网络连接"""
    print("测试基本网络连接...")
    
    test_hosts = [
        ("*******", 53, "Google DNS"),
        ("*******", 53, "Cloudflare DNS"),
        ("www.google.com", 80, "Google HTTP"),
        ("generativelanguage.googleapis.com", 443, "Google AI API")
    ]
    
    results = []
    
    for host, port, description in test_hosts:
        try:
            start_time = time.time()
            sock = socket.create_connection((host, port), timeout=10)
            sock.close()
            end_time = time.time()
            
            latency = (end_time - start_time) * 1000
            print(f"✓ {description}: {latency:.1f}ms")
            results.append((description, True, latency))
            
        except Exception as e:
            print(f"✗ {description}: {str(e)}")
            results.append((description, False, str(e)))
    
    return results


def test_gemini_api_connection(api_key: str = None):
    """测试Gemini API连接"""
    print("\n测试Gemini API连接...")
    
    if not api_key:
        print("⚠️  未提供API密钥，跳过API连接测试")
        return False
    
    try:
        # 配置API
        genai.configure(api_key=api_key)
        
        # 创建模型实例
        model = genai.GenerativeModel(config.GEMINI_MODEL)
        
        # 测试简单的文本生成
        print("发送测试请求...")
        response = model.generate_content("Hello, this is a test.")
        
        if response and response.text:
            print("✓ Gemini API连接成功")
            print(f"  响应: {response.text[:50]}...")
            return True
        else:
            print("✗ Gemini API响应为空")
            return False
            
    except Exception as e:
        print(f"✗ Gemini API连接失败: {str(e)}")
        return False


def test_file_upload(api_key: str = None):
    """测试文件上传功能"""
    print("\n测试文件上传功能...")
    
    if not api_key:
        print("⚠️  未提供API密钥，跳过文件上传测试")
        return False
    
    try:
        # 创建一个小的测试音频文件
        import tempfile
        import wave
        import numpy as np
        
        # 生成1秒的测试音频
        sample_rate = 16000
        duration = 1
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t)
        
        # 转换为16位整数
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # 保存为WAV文件
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
            
            with wave.open(temp_path, 'w') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())
        
        print(f"创建测试音频文件: {temp_path}")
        file_size = os.path.getsize(temp_path) / 1024  # KB
        print(f"文件大小: {file_size:.1f}KB")
        
        # 尝试上传文件
        genai.configure(api_key=api_key)
        
        print("上传测试文件...")
        audio_file = genai.upload_file(temp_path)
        
        print("等待文件处理...")
        max_wait = 30
        wait_time = 0
        
        while audio_file.state.name == "PROCESSING" and wait_time < max_wait:
            time.sleep(1)
            wait_time += 1
            audio_file = genai.get_file(audio_file.name)
        
        if audio_file.state.name == "ACTIVE":
            print("✓ 文件上传和处理成功")
            
            # 清理文件
            genai.delete_file(audio_file.name)
            os.unlink(temp_path)
            print("✓ 测试文件清理完成")
            return True
            
        else:
            print(f"✗ 文件处理失败: {audio_file.state.name}")
            os.unlink(temp_path)
            return False
            
    except Exception as e:
        print(f"✗ 文件上传测试失败: {str(e)}")
        try:
            os.unlink(temp_path)
        except:
            pass
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("网络连接诊断工具")
    print("=" * 60)
    
    # 测试基本网络连接
    network_results = test_basic_network()
    
    # 统计网络连接结果
    successful_connections = sum(1 for _, success, _ in network_results if success)
    total_connections = len(network_results)
    
    print(f"\n基本网络连接: {successful_connections}/{total_connections} 成功")
    
    if successful_connections == 0:
        print("\n❌ 网络连接完全失败")
        print("建议检查：")
        print("1. 网络连接是否正常")
        print("2. 防火墙设置")
        print("3. 代理设置")
        return
    
    # 询问是否测试API
    print("\n" + "=" * 60)
    api_key = input("请输入Gemini API密钥进行API测试（直接回车跳过）: ").strip()
    
    if api_key:
        # 测试API连接
        api_success = test_gemini_api_connection(api_key)
        
        if api_success:
            # 测试文件上传
            upload_success = test_file_upload(api_key)
            
            if upload_success:
                print("\n🎉 所有测试通过！网络连接正常")
            else:
                print("\n⚠️  API连接正常，但文件上传失败")
                print("可能的原因：")
                print("1. API配额不足")
                print("2. 文件格式不支持")
                print("3. 网络不稳定")
        else:
            print("\n❌ API连接失败")
            print("建议检查：")
            print("1. API密钥是否正确")
            print("2. API密钥是否有效")
            print("3. 是否有API使用权限")
            print("4. 网络是否能访问Google服务")
    else:
        print("\n⚠️  跳过API测试")
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)
    
    if successful_connections < total_connections:
        print("\n网络连接存在问题，建议：")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN")
        print("3. 检查防火墙设置")
        print("4. 联系网络管理员")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
    
    input("\n按回车键退出...")
