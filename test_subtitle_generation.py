#!/usr/bin/env python3
"""
字幕生成功能测试脚本
用于测试字幕生成的各个步骤
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from subtitle_generator import SubtitleGenerator
    import config
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


def test_srt_generation():
    """测试SRT文件生成功能"""
    print("测试SRT文件生成功能...")
    
    # 创建测试数据
    chinese_segments = [
        {'start': '00:00', 'end': '00:05', 'text': '欢迎观看这个视频'},
        {'start': '00:05', 'end': '00:10', 'text': '今天我们要讲解的内容是'},
    ]
    
    english_segments = [
        {'start': '00:00', 'end': '00:05', 'text': 'Welcome to this video'},
        {'start': '00:05', 'end': '00:10', 'text': 'Today we will explain the content'},
    ]
    
    # 创建字幕生成器实例（不需要真实API密钥来测试SRT生成）
    generator = SubtitleGenerator("test_key")
    
    # 生成SRT内容
    srt_content = generator.generate_srt_content(chinese_segments, english_segments)
    
    print("生成的SRT内容:")
    print("-" * 40)
    print(srt_content)
    print("-" * 40)
    
    # 测试保存到文件
    test_dir = Path("test_output")
    test_dir.mkdir(exist_ok=True)
    
    test_srt_path = test_dir / "test_subtitle.srt"
    
    try:
        with open(test_srt_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        if test_srt_path.exists() and test_srt_path.stat().st_size > 0:
            print(f"✓ SRT文件保存成功: {test_srt_path}")
            print(f"  文件大小: {test_srt_path.stat().st_size} 字节")
            
            # 读取并验证内容
            with open(test_srt_path, 'r', encoding='utf-8') as f:
                saved_content = f.read()
            
            if saved_content == srt_content:
                print("✓ 文件内容验证成功")
            else:
                print("✗ 文件内容验证失败")
                
        else:
            print("✗ SRT文件保存失败")
            
    except Exception as e:
        print(f"✗ 保存SRT文件时出错: {str(e)}")
    
    # 清理测试文件
    try:
        if test_srt_path.exists():
            test_srt_path.unlink()
        test_dir.rmdir()
        print("✓ 测试文件清理完成")
    except:
        pass


def test_file_path_handling():
    """测试文件路径处理"""
    print("\n测试文件路径处理...")
    
    # 测试不同的路径格式
    test_paths = [
        "C:\\Users\\<USER>\\Videos\\sample.mp4",
        "/home/<USER>/videos/sample.mp4",
        "videos/sample.mp4",
        "sample.mp4"
    ]
    
    for video_path in test_paths:
        try:
            video_name = Path(video_path).stem
            video_dir = Path(video_path).parent
            srt_path = video_dir / f"{video_name}.srt"
            
            print(f"视频路径: {video_path}")
            print(f"  视频名称: {video_name}")
            print(f"  视频目录: {video_dir}")
            print(f"  字幕路径: {srt_path}")
            print()
            
        except Exception as e:
            print(f"✗ 路径处理失败: {video_path} - {str(e)}")


def test_directory_creation():
    """测试目录创建功能"""
    print("测试目录创建功能...")
    
    test_dirs = [
        Path("test_dir1"),
        Path("test_dir2/subdir"),
        Path("test_dir3/sub1/sub2")
    ]
    
    for test_dir in test_dirs:
        try:
            # 确保目录不存在
            if test_dir.exists():
                import shutil
                shutil.rmtree(test_dir.parts[0])
            
            # 创建目录
            test_dir.mkdir(parents=True, exist_ok=True)
            
            if test_dir.exists():
                print(f"✓ 目录创建成功: {test_dir}")
                
                # 测试文件创建
                test_file = test_dir / "test.srt"
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write("测试内容")
                
                if test_file.exists():
                    print(f"✓ 文件创建成功: {test_file}")
                else:
                    print(f"✗ 文件创建失败: {test_file}")
                    
            else:
                print(f"✗ 目录创建失败: {test_dir}")
                
        except Exception as e:
            print(f"✗ 目录操作失败: {test_dir} - {str(e)}")
        
        # 清理测试目录
        try:
            if test_dir.exists():
                import shutil
                shutil.rmtree(test_dir.parts[0])
        except:
            pass


def main():
    """主测试函数"""
    print("=" * 50)
    print("字幕生成功能测试")
    print("=" * 50)
    
    # 运行各项测试
    test_srt_generation()
    test_file_path_handling()
    test_directory_creation()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    print("\n如果所有测试都通过，说明字幕文件保存功能正常。")
    print("如果在实际使用中仍然遇到问题，请检查：")
    print("1. 视频文件路径是否正确")
    print("2. 目标文件夹是否有写入权限")
    print("3. 磁盘空间是否充足")
    print("4. API密钥是否有效")
    print("5. 网络连接是否正常")


if __name__ == "__main__":
    main()
