"""
GUI界面模块
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
import config
from subtitle_generator import SubtitleGenerator


class SubtitleGeneratorUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.generator = None
        self.processing = False
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title(config.APP_TITLE)
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 可选：添加应用图标
            pass
        except:
            pass
    
    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # API密钥输入
        ttk.Label(main_frame, text="Gemini API密钥:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.api_key_var = tk.StringVar(value=config.GEMINI_API_KEY)
        self.api_key_entry = ttk.Entry(main_frame, textvariable=self.api_key_var, show="*", width=50)
        self.api_key_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 添加API密钥帮助按钮
        ttk.Button(main_frame, text="?", width=3, 
                  command=self.show_api_help).grid(row=0, column=2, padx=(5, 0), pady=(0, 5))
        
        # 文件夹选择
        ttk.Label(main_frame, text="视频文件夹:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.folder_var = tk.StringVar()
        self.folder_entry = ttk.Entry(main_frame, textvariable=self.folder_var, width=50)
        self.folder_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(main_frame, text="浏览", 
                  command=self.select_folder).grid(row=1, column=2, padx=(5, 0), pady=(0, 5))
        
        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始生成字幕", 
                                      command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止", 
                                     command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, text="状态:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        self.progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=3, column=1, sticky=tk.W, pady=(0, 5))
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志", 
                  command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
    
    def show_api_help(self):
        """显示API密钥帮助信息"""
        help_text = """
获取Gemini API密钥的步骤：

1. 访问 Google AI Studio: https://makersuite.google.com/app/apikey
2. 登录您的Google账户
3. 点击"Create API Key"创建新的API密钥
4. 复制生成的API密钥并粘贴到上面的输入框中

注意：
- 请妥善保管您的API密钥
- 不要与他人分享您的API密钥
- API密钥将用于调用Google Gemini服务
        """
        messagebox.showinfo("API密钥帮助", help_text.strip())
    
    def select_folder(self):
        """选择视频文件夹"""
        folder = filedialog.askdirectory(title="选择包含视频文件的文件夹")
        if folder:
            self.folder_var.set(folder)
            self.log_message(f"选择文件夹: {folder}")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
    
    def update_progress(self, message):
        """更新进度显示"""
        self.progress_var.set(message)
        self.log_message(message)
    
    def validate_inputs(self):
        """验证输入"""
        api_key = self.api_key_var.get().strip()
        folder_path = self.folder_var.get().strip()
        
        if not api_key:
            messagebox.showerror("错误", "请输入Gemini API密钥")
            return False
        
        if not folder_path:
            messagebox.showerror("错误", "请选择视频文件夹")
            return False
        
        if not os.path.exists(folder_path):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return False
        
        return True
    
    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return
        
        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请等待完成")
            return
        
        # 启动处理线程
        self.processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        thread = threading.Thread(target=self.process_videos, daemon=True)
        thread.start()
    
    def stop_processing(self):
        """停止处理"""
        self.processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.update_progress("已停止")
    
    def process_videos(self):
        """处理视频的后台线程"""
        try:
            api_key = self.api_key_var.get().strip()
            folder_path = self.folder_var.get().strip()

            # 初始化字幕生成器
            self.update_progress("初始化Gemini API...")
            try:
                self.generator = SubtitleGenerator(api_key)
                self.update_progress("✓ API初始化成功")
            except ConnectionError as e:
                error_msg = f"网络连接失败: {str(e)}"
                self.update_progress(error_msg)
                messagebox.showerror("网络错误", f"{error_msg}\n\n请检查：\n1. 网络连接是否正常\n2. 防火墙设置\n3. 是否需要使用VPN")
                return
            except ValueError as e:
                error_msg = f"API密钥错误: {str(e)}"
                self.update_progress(error_msg)
                messagebox.showerror("API密钥错误", f"{error_msg}\n\n请检查：\n1. API密钥是否正确\n2. API密钥是否有效\n3. 是否有API使用权限")
                return

            # 处理文件夹
            self.update_progress("开始处理视频文件...")
            success_count, total_count = self.generator.process_folder(
                folder_path,
                progress_callback=self.update_progress
            )

            # 完成处理
            if self.processing:  # 检查是否被用户停止
                if success_count > 0:
                    self.update_progress(f"✓ 处理完成！成功: {success_count}/{total_count}")
                    messagebox.showinfo("完成", f"字幕生成完成！\n成功处理: {success_count}/{total_count} 个视频\n\n字幕文件已保存到视频所在文件夹")
                else:
                    self.update_progress(f"✗ 处理失败！成功: {success_count}/{total_count}")
                    messagebox.showwarning("处理失败", f"没有成功处理任何视频文件\n\n可能的原因：\n1. 网络连接问题\n2. 音频文件过大\n3. 视频文件没有音轨\n4. API配额不足\n\n请查看日志了解详细信息")

        except Exception as e:
            error_msg = f"处理过程中发生未知错误: {str(e)}"
            self.update_progress(error_msg)
            messagebox.showerror("错误", f"{error_msg}\n\n请尝试：\n1. 重启程序\n2. 检查视频文件\n3. 使用调试版本获取详细日志")

        finally:
            # 重置UI状态
            self.processing = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            if not self.processing:
                self.update_progress("就绪")
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    app = SubtitleGeneratorUI()
    app.run()


if __name__ == "__main__":
    main()
